#separator:tab
#html:true
#guid column:1
#notetype column:2
#deck column:3
#tags column:7
JS_STRING_SUBSTRING_VS_SUBSTR	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	`substring()` 和 `substr()` 这两个JavaScript字符串方法有什么共同点和主要区别？	"<ul><li><b>共同点：</b>都用于截取字符串的一部分。</li><li><b>主要区别：</b>参数定义不同。</li></ul>"	"[{""title"": ""JavaScript中 `substring()` 方法的参数和作用是什么？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_STRING_SUBSTRING_DEF""}, {""title"": ""JavaScript中 `substr()` 方法的参数和作用是什么？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_STRING_SUBSTR_DEF""}]"	JavaScript 字符串
JS_STRING_SUBSTRING_DEF	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript中 `substring()` 方法的参数和作用是什么？	"<b>方法:</b> `substring(startIndex, endIndex)`<br><b>作用:</b> 截取从 `startIndex` 开始，到 `endIndex` <b>之前</b>（不包括 `endIndex`）的字符。<br>如果省略 `endIndex`，则截取到字符串末尾。"		JavaScript 字符串
JS_STRING_SUBSTR_DEF	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript中 `substr()` 方法的参数和作用是什么？	"<b>方法:</b> `substr(startIndex, length)`<br><b>作用:</b> 从 `startIndex` 开始，截取指定 `length` 数量的字符。<br>如果省略 `length`，则截取到字符串末尾。"		JavaScript 字符串
JS_ASYNC_SCRIPT_LOAD	AnkiNexus	前端::5-浏览器与WebAPI::BOM与事件	在Web中，实现JavaScript脚本异步加载有哪两种主要方式？	"<ul><li>1. 动态创建 `&lt;script&gt;` 标签。</li><li>2. 使用 `XMLHttpRequest` 或 `Fetch API` 异步请求脚本内容后执行。</li></ul>"	"[{""title"": ""如何通过动态创建`<script>`标签来实现JS脚本的异步加载？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件"", ""note_guid"": ""JS_ASYNC_SCRIPT_CREATE_ELEMENT""}, {""title"": ""如何通过XHR或Fetch API来实现JS脚本的异步加载？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件"", ""note_guid"": ""JS_ASYNC_SCRIPT_XHR_FETCH""}, {""title"": ""与同步加载相比，JS脚本异步加载有哪些主要优点？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件"", ""note_guid"": ""JS_ASYNC_SCRIPT_ADVANTAGES""}]"	JavaScript 异步加载
JS_ASYNC_SCRIPT_CREATE_ELEMENT	基础	前端::5-浏览器与WebAPI::BOM与事件	如何通过动态创建`<script>`标签来实现JS脚本的异步加载？	"<pre><code>const script = document.createElement('script');
script.src = 'path/to/script.js';

// 通过onload事件监听加载完成
script.onload = function() {
  // 脚本加载完成后执行的回调函数
};

document.body.appendChild(script);</code></pre>这种方式通过 `onload` 或 `onreadystatechange` 事件来检测脚本是否加载完成。"		JavaScript 异步加载
JS_ASYNC_SCRIPT_XHR_FETCH	基础	前端::5-浏览器与WebAPI::BOM与事件	如何通过XHR或Fetch API来实现JS脚本的异步加载？	"基本思路是：使用 `XMLHttpRequest` 或 `Fetch API` 发送异步请求获取脚本文件内容，然后通过 `eval()` 或动态创建包含脚本内容的 `&lt;script&gt;` 标签来执行。<br><br><b>示例 (XHR):</b><pre><code>const xhr = new XMLHttpRequest();
xhr.open('GET', 'path/to/script.js');
xhr.onload = function() {
  if (xhr.status === 200) {
    const script = document.createElement('script');
    script.textContent = xhr.responseText;
    document.head.appendChild(script);
  }
};
xhr.send();</code></pre>"		JavaScript 异步加载
JS_ASYNC_SCRIPT_ADVANTAGES	基础	前端::5-浏览器与WebAPI::BOM与事件	与同步加载相比，JS脚本异步加载有哪些主要优点？	"<ul><li><b>提升页面加载速度：</b>避免了JavaScript下载和执行时对页面渲染的阻塞，从而加快页面整体加载和响应速度。</li><li><b>避免资源加载阻塞：</b>允许浏览器并行加载其他资源（如图片、CSS），而不是等待脚本加载完成。</li><li><b>灵活控制加载与执行：</b>可以根据业务需求动态地、按需地加载和执行脚本，提高了代码的可维护性和可扩展性。</li></ul>"		JavaScript 异步加载
JS_FOR_IN_VS_FOR_OF	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	`for...in` 和 `for...of` 循环在JavaScript中的核心区别是什么？	"<ul><li><b>`for...in`</b>: 遍历对象<b>所有可枚举属性</b>的<b>键名 (key)</b>，包括原型链上的属性。</li><li><b>`for...of`</b>: 遍历<b>可迭代对象 (Iterable)</b> 的<b>元素值 (value)</b>。</li></ul>"	"[{""title"": ""JavaScript中的 `for...in` 循环是用来做什么的？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_FOR_IN_DEF""}, {""title"": ""JavaScript中的 `for...of` 循环是用来做什么的？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_FOR_OF_DEF""}]"	JavaScript 循环
JS_FOR_IN_DEF	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript中的 `for...in` 循环是用来做什么的？	"<b>用途：</b>用于遍历一个对象的<b>所有可枚举属性的键名（key 或 property name）</b>。<br><br><b>特点：</b><ul><li>它会遍历对象自身的属性，以及其原型链上所有可枚举的属性。</li><li>因此，通常不推荐用 `for...in` 来遍历数组，因为它可能会遍历到非预期的属性（如数组原型上的方法）。</li></ul><b>示例：</b><pre><code>const obj = { a: 1, b: 2 };
for (const key in obj) {
  console.log(key); // 输出 'a', 'b'
}</code></pre>"		JavaScript 循环
JS_FOR_OF_DEF	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript中的 `for...of` 循环是用来做什么的？	"<b>用途：</b>用于遍历<b>可迭代对象（Iterable）</b>的<b>元素值（value）</b>。<br><br><b>可迭代对象包括：</b>`Array`, `String`, `Map`, `Set`, `arguments` 对象等。<br><br><b>特点：</b><ul><li>它只遍历对象自身的元素，不会遍历原型链。</li><li>不能用于遍历普通的非迭代对象（Plain Object）。</li></ul><b>示例：</b><pre><code>const arr = ['a', 'b'];
for (const value of arr) {
  console.log(value); // 输出 'a', 'b'
}</code></pre>"		JavaScript 循环
JS_TYPE_CHECKING	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	在JavaScript中，判断数据类型的常用方法有哪些？	"<ul><li>1. `typeof` 操作符</li><li>2. `instanceof` 操作符</li><li>3. `Object.prototype.toString.call()`</li><li>4. `Array.isArray()` (专门用于数组)</li></ul>"	"[{""title"": ""在JavaScript中，`typeof` 操作符如何用于判断数据类型？有什么特点和局限性？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::typeof"", ""note_guid"": ""JS_TYPE_CHECK_TYPEOF""}, {""title"": ""在JavaScript中，`instanceof` 操作符如何用于判断数据类型？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::instanceof"", ""note_guid"": ""JS_TYPE_CHECK_INSTANCEOF""}, {""title"": ""为什么 `Object.prototype.toString.call()` 被认为是JavaScript中最可靠的类型判断方法？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_TYPE_CHECK_TOSTRING""}]"	JavaScript 数据类型
JS_TYPE_CHECK_TYPEOF	基础	前端::3-JavaScript::1-语法与数据结构::typeof	在JavaScript中，`typeof` 操作符如何用于判断数据类型？有什么特点和局限性？	"<b>作用：</b>返回一个表示未经计算的操作数类型的字符串。<br><br><b>返回值：</b>`"undefined"`, `"boolean"`, `"number"`, `"string"`, `"symbol"`, `"bigint"`, `"function"`, `"object"`。<br><br><b>特点：</b>对于原始类型（除 `null` 外）和 `function` 类型，`typeof` 能给出准确的结果。<br><br><b>局限性：</b><ul><li>`typeof null` 返回 `"object"`，这是一个历史遗留问题。</li><li>对于所有引用类型（如 `Array`, `Date`, `RegExp` 等，`function` 除外），`typeof` 都会返回 `"object"`，无法进一步区分。</li></ul>"		JavaScript 数据类型 typeof
JS_TYPE_CHECK_INSTANCEOF	基础	前端::3-JavaScript::1-语法与数据结构::instanceof	在JavaScript中，`instanceof` 操作符如何用于判断数据类型？	"<b>作用：</b>`instanceof` 操作符用于检测构造函数的 `prototype` 属性是否出现在某个实例对象的原型链上。<br><br><b>语法：</b>`object instanceof constructor`<br><br><b>用途：</b>主要用于判断一个对象是否是某个特定类或构造函数的实例，可以区分数组、日期、正则等具体的引用类型。<br><br><b>示例：</b><pre><code>const arr = [];
const date = new Date();

arr instanceof Array;  // true
date instanceof Date; // true
arr instanceof Object; // true (因为Array继承自Object)</code></pre><b>局限性：</b>它无法用于判断原始数据类型（string, number, boolean, null, undefined, symbol）。"		JavaScript 数据类型 instanceof
JS_TYPE_CHECK_TOSTRING	基础	前端::3-JavaScript::1-语法与数据结构	为什么 `Object.prototype.toString.call()` 被认为是JavaScript中最可靠的类型判断方法？	"<b>原因：</b>`Object.prototype.toString()` 方法会返回一个 `'[object Type]'` 格式的字符串，其中 `Type` 是对象的内部 [[Class]] 属性。这个属性能够精确地表示任何值的内置类型，包括原始类型和引用类型。<br><br><b>用法：</b>必须使用 `.call()` 或 `.apply()` 来调用，并传入要检查的值。<br><br><b>示例：</b><pre><code>Object.prototype.toString.call(null);      // [object Null]
Object.prototype.toString.call([]);        // '[object Array]'
Object.prototype.toString.call({});        // '[object Object]'
Object.prototype.toString.call(new Date());  // '[object Date]'</code></pre>这种方法克服了 `typeof` 对引用类型区分不清和对 `null` 误判的问题，也比 `instanceof` 更通用（可用于原始类型）。"		JavaScript 数据类型
JS_ARRAY_SPLICE_VS_SLICE	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	`splice()` 和 `slice()` 这两个JavaScript数组方法在功能和对原数组的影响上有何核心区别？	"<ul><li><b>`splice()`:</b> <b>会改变原数组</b>。它可以用于在数组中添加、删除或替换元素。</li><li><b>`slice()`:</b> <b>不会改变原数组</b>。它返回一个从原数组中提取的元素组成的<b>新数组</b>。</li></ul>"	"[{""title"": ""JavaScript数组方法 `splice()` 的功能和用法是什么？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_ARRAY_SPLICE_DEF""}, {""title"": ""JavaScript数组方法 `slice()` 的功能和用法是什么？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_ARRAY_SLICE_DEF""}, {""title"": ""在JavaScript中，有哪几种方法可以删除数组的最后一个元素？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_ARRAY_DELETE_LAST""}]"	JavaScript 数组
JS_ARRAY_SPLICE_DEF	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript数组方法 `splice()` 的功能和用法是什么？	"<b>作用：</b>通过删除或替换现有元素或者原地添加新的元素来修改数组，并以数组形式返回被修改的内容。<b>此方法会改变原数组。</b><br><br><b>语法：</b>`array.splice(start, deleteCount, item1, item2, ...)`<br><ul><li>`start`: 修改的开始位置。</li><li>`deleteCount` (可选): 表示要移除的数组元素的个数。</li><li>`item1, item2, ...` (可选): 要添加进数组的元素。</li></ul>"		JavaScript 数组
JS_ARRAY_SLICE_DEF	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript数组方法 `slice()` 的功能和用法是什么？	"<b>作用：</b>返回一个新的数组对象，这一对象是一个由 `begin` 和 `end` 决定的原数组的浅拷贝（包括 `begin`，不包括 `end`）。<b>原始数组不会被改变。</b><br><br><b>语法：</b>`arr.slice(begin, end)`<br><ul><li>`begin` (可选): 提取起始处的索引。</li><li>`end` (可选): 提取终止处的索引（不包括该索引）。如果省略，`slice`会一直提取到原数组末尾。</li></ul>"		JavaScript 数组
JS_ARRAY_DELETE_LAST	基础	前端::3-JavaScript::1-语法与数据结构	在JavaScript中，有哪几种方法可以删除数组的最后一个元素？	"主要有三种方法：<br><ol><li><b>`pop()`:</b> 这是最常用和最直接的方法。它会删除数组的最后一个元素，并返回该元素。<b>会改变原数组。</b><pre><code>const arr = [1, 2, 3];
arr.pop(); // 返回 3, arr 变为 [1, 2]</code></pre></li><li><b>`splice()`:</b> 使用 `splice` 方法，指定从倒数第一个位置开始删除一个元素。<b>会改变原数组。</b><pre><code>const arr = [1, 2, 3];
arr.splice(-1, 1); // arr 变为 [1, 2]</code></pre></li><li><b>`slice()`:</b> `slice` 本身不改变原数组，但可以结合它来创建一个不包含最后一个元素的新数组。<pre><code>const arr = [1, 2, 3];
const newArr = arr.slice(0, -1); // newArr 为 [1, 2]</code></pre></li></ol>"		JavaScript 数组
JS_EQUALITY_DOUBLE_VS_TRIPLE	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	JavaScript中的相等运算符 `==` 和 `===` 的核心区别是什么？	"<ul><li><b>`==` (相等运算符):</b> 在比较时会进行<b>类型转换 (Type Coercion)</b>，如果两边值的类型不同，会尝试将它们转换为相同类型后再比较。</li><li><b>`===` (严格相等运算符):</b> <b>不进行类型转换</b>。只有当两个值的类型和值都完全相同时，才返回 `true`。</li></ul><br><b>最佳实践：</b>为了代码的严谨性和可预测性，应优先使用 `===`。 "	"[{""title"": ""JavaScript的 `==` (相等)运算符存在哪些隐式类型转换规则？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_EQUALITY_DOUBLE_RULES""}, {""title"": ""为什么在JavaScript中推荐优先使用 `===` (严格相等)运算符？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_EQUALITY_TRIPLE_REASON""}]"	JavaScript 相等性
JS_EQUALITY_DOUBLE_RULES	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript的 `==` (相等)运算符存在哪些隐式类型转换规则？	"主要的转换规则包括：<br><ul><li>如果一个值是 `null`，另一个是 `undefined`，则它们相等 (`null == undefined` is `true`)。</li><li>如果一个是数字，另一个是字符串，会将字符串转换为数字再比较。(`1 == '1'` is `true`)</li><li>如果一个值是布尔值，会将布尔值转换为数字（`true` -> `1`, `false` -> `0`）再比较。(`true == 1` is `true`)</li><li>如果一个值是对象，另一个是数字或字符串，会尝试将对象转换为原始值（通过 `toString()` 或 `valueOf()` 方法）再比较。</li></ul>"		JavaScript 相等性
JS_EQUALITY_TRIPLE_REASON	基础	前端::3-JavaScript::1-语法与数据结构	为什么在JavaScript中推荐优先使用 `===` (严格相等)运算符？	"推荐使用 `===` 的主要原因是为了<b>避免意外的类型转换</b>，从而提高代码的<b>可预测性、严谨性和安全性</b>。<br><br>由于 `==` 的转换规则复杂且不直观，很容易导致难以发现的bug。例如 `'' == 0` 和 `'' == false` 都返回 `true`。<br><br>使用 `===` 可以确保只有在类型和值都完全匹配的情况下才判定为相等，这让代码的意图更加清晰，减少了出错的可能性。"		JavaScript 相等性
JS_BROWSER_ANIMATION_API	AnkiNexus	前端::5-浏览器与WebAPI::BOM与事件	`requestAnimationFrame` 和 `requestIdleCallback` 这两个浏览器API分别是什么，它们的核心用途有何不同？	"<ul><li><b>`requestAnimationFrame` (rAF):</b> <b>用于动画渲染</b>。它请求浏览器在下一次重绘（repaint）之前执行一个回调函数，确保动画的流畅性。</li><li><b>`requestIdleCallback` (rIC):</b> <b>用于低优先级任务</b>。它请求浏览器在主线程处于空闲状态时执行一个回调函数，用于处理一些非关键的、可延迟的后台任务，避免阻塞关键事件（如动画和输入响应）。</li></ul>"	"[{""title"": ""`requestAnimationFrame` (rAF) 的工作机制和主要用途是什么？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件"", ""note_guid"": ""JS_RAF_DEF""}, {""title"": ""`requestIdleCallback` (rIC) 的工作机制和主要用途是什么？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件"", ""note_guid"": ""JS_RIC_DEF""}]"	JavaScript 浏览器API
JS_RAF_DEF	基础	前端::5-浏览器与WebAPI::BOM与事件	`requestAnimationFrame` (rAF) 的工作机制和主要用途是什么？	"<b>工作机制：</b>`requestAnimationFrame` 告诉浏览器你希望执行一个动画，并请求浏览器在下一次重绘之前调用指定的回调函数来更新动画。该回调函数执行次数通常与显示器的刷新率保持一致（例如，每秒60次）。<br><br><b>主要用途：</b>创建高性能、流畅的JavaScript动画。浏览器可以优化并行的动画动作，更合理地安排重绘，从而避免丢帧，减少CPU、GPU的消耗。它比使用 `setTimeout` 或 `setInterval` 来实现动画效果要高效得多。"		JavaScript 浏览器API
JS_RIC_DEF	基础	前端::5-浏览器与WebAPI::BOM与事件	`requestIdleCallback` (rIC) 的工作机制和主要用途是什么？	"<b>工作机制：</b>`requestIdleCallback` 会在浏览器主线程的空闲时段内调用一个函数排队。这允许开发者在不干扰延迟关键事件（如动画和输入响应）的情况下执行后台和低优先级工作。<br><br><b>回调参数：</b>回调函数会接收一个 `IdleDeadline` 对象作为参数，该对象提供了一个 `timeRemaining()` 方法，可以获取当前空闲周期的剩余时间，以便在空闲时间用尽前停止任务。<br><br><b>主要用途：</b>执行一些可以被延迟的、非必要的任务，例如发送统计数据、预加载数据、或者在后台执行复杂的计算。"		JavaScript 浏览器API
JS_LET_GLOBAL_SCOPE	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包	在全局作用域中使用 `let` 声明的变量，是否能通过 `window` 对象访问到？	"<b>不能。</b><br><br>使用 `let` (以及 `const`) 在全局作用域中声明的变量，虽然也是全局变量，但它们不会被创建为全局对象 (`window`) 的属性。它们存在于一个独立的、被称为“脚本作用域”（Script Scope）的词法环境中。<br><br>这与 `var` 不同，使用 `var` 在全局作用域声明的变量会成为 `window` 对象的属性。<br><br><b>示例：</b><pre><code>let foo = 'bar';
var baz = 'qux';

console.log(window.foo); // undefined
console.log(window.baz); // 'qux'</code></pre>"		JavaScript 作用域 let
JS_FOREACH_BREAK	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript数组的 `forEach` 方法是否可以中途结束循环？如果不能，有什么替代方案？	"<b>不能直接结束。</b><br><br>`forEach` 方法被设计为总是遍历数组中的每一个元素，它没有提供像 `for` 循环中的 `break` 或 `return` (返回到外部函数) 那样的机制来提前终止循环。<br><br><b>替代方案：</b><br><ol><li><b>使用传统的 `for` 循环：</b>这是最直接的方式，可以使用 `break` 和 `continue`。<pre><code>for (let i = 0; i < arr.length; i++) {
  if (condition) break;
}</code></pre></li><li><b>使用 `for...of` 循环：</b>同样支持 `break` 和 `continue`。<pre><code>for (const item of arr) {
  if (condition) break;
}</code></pre></li><li><b>使用 `some()` 或 `every()` 方法：</b><ul><li>`some()`: 当回调函数返回 `true` 时，循环立即停止。可用于模拟 `break`。</li><li>`every()`: 当回调函数返回 `false` 时，循环立即停止。</li></ul></li></ol><b>不推荐的方式：</b>通过 `try...catch` 抛出异常来强行中断 `forEach`，这会使代码难以理解和维护。"		JavaScript 数组 forEach
JS_CANVAS_POINT_IN_PATH	AnkiNexus	前端::5-浏览器与WebAPI::Canvas	在HTML5 Canvas中，如何判断一个点是否在一个已绘制的图形内部？	"主要有两种方法：<br><ol><li><b>使用Canvas内置API：</b>对于由路径（Path）定义的图形，可以使用 `ctx.isPointInPath(x, y)` 方法。</li><li><b>使用数学算法：</b>对于复杂的或未形成路径的图形，需要自行实现算法，最常用的是<b>射线法 (Ray-Casting Algorithm)</b>。</li></ol>"	"[{""title"": ""如何使用 Canvas API 的 `isPointInPath()` 方法来判断点是否在图形内？"", ""deck"": ""前端::5-浏览器与WebAPI::Canvas"", ""note_guid"": ""JS_CANVAS_ISPOINTINPATH""}, {""title"": ""什么是射线法 (Ray-Casting Algorithm)，它如何用于判断点是否在多边形内部？"", ""deck"": ""前端::5-浏览器与WebAPI::Canvas"", ""note_guid"": ""JS_CANVAS_RAY_CASTING""}]"	JavaScript Canvas
JS_CANVAS_ISPOINTINPATH	基础	前端::5-浏览器与WebAPI::Canvas	如何使用 Canvas API 的 `isPointInPath()` 方法来判断点是否在图形内？	"<b>方法：</b>`isPointInPath(x, y)` 是 Canvas 2D 上下文的一个方法。<br><br><b>工作流程：</b><br><ol><li>首先，使用路径绘制方法（如 `beginPath()`, `moveTo()`, `lineTo()`, `arc()`, `rect()` 等）来定义一个封闭的图形路径。</li><li>然后，调用 `ctx.isPointInPath(x, y)`，传入要检测的点的坐标。</li><li>如果该点在当前定义的路径内部，方法返回 `true`；否则返回 `false`。</li></ol><b>示例：</b><pre><code>ctx.beginPath();
ctx.rect(20, 20, 100, 100);

// 检测点 (50, 50) 是否在矩形内
console.log(ctx.isPointInPath(50, 50)); // true

// 检测点 (150, 150) 是否在矩形内
console.log(ctx.isPointInPath(150, 150)); // false</code></pre>"		JavaScript Canvas
JS_CANVAS_RAY_CASTING	基础	前端::5-浏览器与WebAPI::Canvas	什么是射线法 (Ray-Casting Algorithm)，它如何用于判断点是否在多边形内部？	"<b>射线法 (Ray-Casting Algorithm)</b> 是一种用于判断一个点是否在多边形内部的经典算法。<br><br><b>原理：</b><br>从该点出发，向任意固定方向（例如，水平向右）引出一条射线。然后，计算这条射线与多边形所有边的交点数量。<br><ul><li>如果交点数量为<b>奇数</b>，则该点在多边形<b>内部</b>。</li><li>如果交点数量为<b>偶数</b>（包括0），则该点在多边形<b>外部</b>。</li></ul><b>注意：</b>需要处理一些特殊情况，比如射线恰好穿过多边形的顶点，或者与多边形的某条边重合。"		JavaScript Canvas
JS_OBJECT_MERGE	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	在JavaScript中，如何合并两个或多个对象？	"主要有两种现代且常用的方式：<br><ol><li><b>`Object.assign()` 方法：</b>将所有可枚举属性的值从一个或多个源对象复制到目标对象。</li><li><b>扩展运算符 `...` (Spread Operator)：</b>在一个新的对象字面量中展开源对象的属性。</li></ol><b>注意：</b>这两种方法都是浅拷贝。如果合并的对象中有同名属性，后面的对象的属性值会覆盖前面的。"	"[{""title"": ""如何使用 `Object.assign()` 来合并JavaScript对象？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_OBJECT_MERGE_ASSIGN""}, {""title"": ""如何使用扩展运算符 `...` 来合并JavaScript对象？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_OBJECT_MERGE_SPREAD""}]"	JavaScript 对象
JS_OBJECT_MERGE_ASSIGN	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 `Object.assign()` 来合并JavaScript对象？	"<b>语法：</b> `Object.assign(target, ...sources)`<br><br>它将所有源对象 (`sources`) 的可枚举属性复制到目标对象 (`target`)，并返回修改后的 `target` 对象。<br><br><b>示例：</b><pre><code>const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };

// 为了不修改原对象，通常提供一个空对象作为target
const mergedObj = Object.assign({}, obj1, obj2);

console.log(mergedObj); // { a: 1, b: 2, c: 3, d: 4 }</code></pre>"		JavaScript 对象
JS_OBJECT_MERGE_SPREAD	基础	前端::3-JavaScript::1-语法与数据结构	如何使用扩展运算符 `...` 来合并JavaScript对象？	"扩展运算符 (`...`) 提供了一种更简洁的语法来合并对象。<br><br><b>语法：</b>在一个新的对象字面量中，将要合并的对象展开。<br><br><b>示例：</b><pre><code>const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };

const mergedObj = { ...obj1, ...obj2 };

console.log(mergedObj); // { a: 1, b: 2, c: 3, d: 4 }</code></pre>这种方式因其简洁性在ES6+中非常流行。"		JavaScript 对象
JS_OBJECT_IS_EMPTY	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	在JavaScript中，如何判断一个对象是否为空对象（即没有任何自身属性）？	"主要有两种常用方法：<br><ol><li><b>使用 `Object.keys()`:</b> 获取对象所有可枚举的自身属性键名组成的数组，然后检查其 `length` 是否为 0。</li><li><b>使用 `for...in` 循环:</b> 尝试遍历对象，如果循环体能够执行，说明对象有属性，则不为空。</li></ol>"	"[{""title"": ""如何使用 `Object.keys()` 来判断一个JavaScript对象是否为空？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_OBJECT_IS_EMPTY_KEYS""}, {""title"": ""如何使用 `for...in` 循环来判断一个JavaScript对象是否为空？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_OBJECT_IS_EMPTY_FORIN""}]"	JavaScript 对象
JS_OBJECT_IS_EMPTY_KEYS	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 `Object.keys()` 来判断一个JavaScript对象是否为空？	"<b>方法：</b>`Object.keys(obj)` 会返回一个由给定对象自身的可枚举属性名组成的数组。<br><br>通过检查这个数组的 `length` 属性是否为 `0`，就可以判断该对象是否为空。<br><br><b>示例：</b><pre><code>function isEmpty(obj) {
  return Object.keys(obj).length === 0;
}

const obj1 = {};
const obj2 = { a: 1 };

console.log(isEmpty(obj1)); // true
console.log(isEmpty(obj2)); // false</code></pre>这是目前最常用和最直接的方法。"		JavaScript 对象
JS_OBJECT_IS_EMPTY_FORIN	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 `for...in` 循环来判断一个JavaScript对象是否为空？	"<b>方法：</b>`for...in` 循环会遍历一个对象（包括其原型链）的可枚举属性。我们可以利用它来检查一个对象是否有任何可枚举的自身属性。<br><br><b>示例：</b><pre><code>function isEmpty(obj) {
  for (const prop in obj) {
    // hasOwnProperty确保是自身属性而非原型链上的
    if (Object.prototype.hasOwnProperty.call(obj, prop)) {
      return false; // 一旦发现有自身属性，立即返回false
    }
  }
  return true; // 如果循环结束都没有返回，说明是空对象
}

console.log(isEmpty({}));      // true
console.log(isEmpty({a: 1})); // false</code></pre><b>注意：</b>必须配合 `hasOwnProperty` 来排除原型链上的属性。"		JavaScript 对象
