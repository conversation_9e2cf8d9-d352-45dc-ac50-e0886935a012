+ 体系化、结构化答题的重要性

前端面试中，体系化和解构化答题可以帮助你更好地组织自己的思维，提高回答问题的质量和效率。展示自己的技能和实战经验，从而在面试中取得更好的表现。大家可以对比自己平时面试中答题的情况，是否足够体系化，还是东一块西一块，说完了，不但面试官没有听懂，自己都干懵了。面试效果可想而之！总结、复盘十分重要，同时我们要做好大量的练习！从下面这些方面发力，肯定会打出不错的价值！

1. 展现知识体系：前端知识点较多，面试者需要对各个知识点进行深入理解和掌握，通过体系化、结构化的回答方式，可以更清晰、完整地展现自己的知识体系。
2. 提升答题效率：面试时间有限，如何在短时间内回答多个问题是面试者需要解决的难题。体系化、结构化的回答方式可以帮助面试者在快速思考的同时，将答案更好地组织起来，提升答题效率。
3. 防止遗漏问题：在面试过程中，面试官会问到各种不同的问题，有时候一些问题容易被遗漏。通过体系化、结构化的答题方式，可以帮助面试者系统化地思考，降低遗漏问题的可能性。
4. 表现专业能力：体系化、结构化的答题方式可以帮助面试者将自己的思路清晰地表达出来，更好地展现自己的专业能力和技能水平。
5. 提高回答质量：体系化、结构化的答题方式可以帮助面试者更深入地分析问题，更完整地回答问题，提高回答质量。



<h1 id="bDATt">**instanceof的原理**</h1>
<h3 id="NOZWG">讲概念</h3>
1. instanceof运算符用于检测构造函数的 prototype 属性是否出现在某个实例对象的原型链上
2. **面试官如果追问**：详细来说：instanceof运算符的原理是通过检查object的原型链是否包含constructor的原型对象。如果object的原型链中存在constructor的原型对象，那么object就是constructor的一个实例，返回值为true。如果object的原型链中不存在constructor的原型对象，那么object就不是constructor的实例，返回值为false



<h3 id="d9YHJ">缺点</h3>
由于instanceof是基于原型链的检查，因此如果某个对象的原型链比较深，那么检查的效率会比较低



<h3 id="KvbDZ">手写</h3>
```javascript
function myInstanceOf(obj, constructor) {
  let proto = Object.getPrototypeOf(obj); // 获取 obj 的原型
  while (proto) {
    if (proto === constructor.prototype) {
      return true;
    }
    proto = Object.getPrototypeOf(proto); // 获取原型链上的下一个原型
  }
  return false;
}
```



<h3 id="LtAmp">理解案例</h3>
例如，对于以下代码：

```javascript
function Animal() {}
function Cat() {}
Cat.prototype = new Animal();
var cat = new Cat();
console.log(cat instanceof Cat); // true
console.log(cat instanceof Animal); // true
```

cat是一个Cat的实例，因为Cat.prototype是Animal的一个实例，所以cat的原型链上包含Animal.prototype，从而cat instanceof Animal也返回true。



<h3 id="B2u3D">拓展</h3>
需要注意的是，instanceof运算符只能用于检查对象是否是某个构造函数的实例，不能用于基本类型（如字符串、数字等）的检查。如果检查的对象不是一个对象类型，instanceof会输出false。此外，由于instanceof是基于原型链的检查，因此如果某个对象的原型链比较深，那么检查的效率会比较低。

instanceof操作符判断的是对象的原型链，因此如果一个对象是某个类的实例，那么它一定是该类的原型链上的某个对象的实例。因此，如果一个对象的原型链上没有该类的原型对象，那么它就不是该类的实例，即使它与该类具有相同的属性和方法。



<h1 id="bif2F">new 的原理</h1>
<h3 id="o49kW">讲概念</h3>
new 运算符创建一个用户定义的对象类型的实例或具有构造函数的内置对象的实例。



<h3 id="iSyp9">**追问new具体做了哪些事**</h3>
1、new操作符具体做了什么

new 操作符用于创建一个新的对象，并将该对象绑定到构造函数（constructor）的 this 上。具体来说，new 操作符的执行过程可以分为以下几个步骤：

1. 创建一个空对象，该对象的原型为构造函数的原型对象。
2. 将构造函数的 this 绑定到该空对象上，即执行 this = Object.create(Constructor.prototype)。
3. 执行构造函数的代码，并将属性和方法添加到该空对象中。
4. 如果构造函数没有显式返回一个对象，则返回该空对象；否则，返回构造函数显式返回的对象。



<h3 id="kbOP0">案例理解</h3>
下面是一个使用 new 操作符创建对象的示例：

```javascript
function Person(name, age) {
 this.name = name;
 this.age = age;
}
var john = new Person('John', 30);
console.log(john); // {name: 'John', age: 30}
```



在上面的示例中，new 操作符创建了一个空对象，并将该对象绑定到 Person 构造函数的 this 上。然后，执行了 Person 构造函数的代码，并将 name 和 age 属性添加到该空对象中。最后，返回了该对象，即 john。

需要注意的是，虽然使用 new 操作符创建的对象具有构造函数的原型对象作为其原型，但它并不等同于构造函数本身，而是一个新的对象。



<h3 id="PMMep">手写new</h3>
下面是一个简单的手写 new 的实现：

```javascript
function myNew(Constructor, ...args) {
  // 创建一个空对象，该对象的原型为构造函数的原型对象
  var obj = Object.create(Constructor.prototype);
  // 将构造函数的 this 绑定到该空对象上，执行构造函数的代码
  var result = Constructor.apply(obj, args);
  // 如果构造函数有显式返回一个对象，则返回该对象，否则返回空对象
  return (typeof result === 'object' && result !== null) ? result : obj;
}
```



使用 myNew 函数创建对象的方式与使用 new 操作符相同，例如：

```javascript
function Person(name, age) {
 this.name = name;
 this.age = age;
}
var john = myNew(Person, 'John', 30);
console.log(john); // {name: 'John', age: 30}
```



在上面的示例中，myNew 函数创建了一个空对象，并将该对象绑定到 Person 构造函数的 this 上。然后，执行了 Person 构造函数的代码，并将 name 和 age 属性添加到该空对象中。最后，返回了该对象，即 john。

需要注意的是，这个实现并不完整，它没有处理构造函数的原型链和 arguments 对象等细节，仅用于演示 new 操作符的基本原理。在实际开发中，应该使用标准的 new 操作符，或者使用成熟的类库和框架来处理对象创建和继承等问题。



<h1 id="lrFG0">js的作用域</h1>
<h3 id="HtgMH">讲概念</h3>
在 JavaScript 中，作用域（Scope）是指在程序中定义变量的可见范围。

<h3 id="srU4E">详细举例</h3>
JavaScript 中的作用域分为全局作用域和函数作用域。

全局作用域是指在代码中任何地方都可以访问的变量，它们在整个应用程序中都是可见的。

函数作用域是指在函数内部定义的变量，它们只能在该函数内部访问，函数外部无法访问这些变量。

JavaScript 中还有一个特殊的作用域，即块级作用域。在 ES6 之前，JavaScript 中没有块级作用域，只有全局作用域和函数作用域。在 ES6 中，使用 let 和 const 关键字可以创建块级作用域。块级作用域是指在代码块（通常是指用花括号包含的语句集合）中定义的变量，它们只能在该代码块内部访问，代码块外部无法访问这些变量。

<h3 id="eZar7">总结收敛</h3>
在 JavaScript 中，变量的作用域是由它们在代码中声明的位置所决定的。当程序执行时，JavaScript 引擎会根据变量声明的位置来确定变量的作用域。通常，let和const变量在声明的位置下方的代码都可以访问该变量，而在声明位置上方的代码则无法访问该变量。



<h1 id="eXwl3">js事件流</h1>
<h3 id="aQ076">讲概念</h3>
JavaScript 事件流（Event flow）指的是浏览器处理事件的方式。

<h3 id="viM6M">详细说</h3>
在 JavaScript 中，事件流分为三个阶段：捕获阶段、目标阶段和冒泡阶段。这三个阶段是从外到内，从父节点到子节点，再从子节点到父节点的顺序依次发生。

事件流的过程如下：

1. **捕获阶段：**事件从最外层的节点（文档对象）开始，逐级向下传播，直到达到事件的目标节点。
2. **目标阶段：**事件到达目标节点，触发目标节点上的事件处理函数。
3. **冒泡阶段：**事件从目标节点开始，逐级向上传播，直到达到最外层的节点（文档对象）。

[https://juejin.cn/post/7247845549734314039?searchId=2023081114155919B931FBDB03589BC6F0](https://juejin.cn/post/7247845549734314039?searchId=2023081114155919B931FBDB03589BC6F0)

<h3 id="pq9Ji">拓展答</h3>
在事件流中，事件的传播过程可以被停止或取消。在事件处理函数中，可以使用 event.stopPropagation() 方法阻止事件的进一步传播，或者使用 event.preventDefault() 方法取消事件的默认行为。



<h3 id="Lngp3">总结收敛</h3>
事件流是指描述事件在页面元素中传播的顺序，包括事件捕获阶段、目标阶段和事件冒泡阶段。在事件捕获阶段中，事件从最外层的元素开始向下传递，直到目标元素；在目标阶段中，事件在目标元素上触发；在事件冒泡阶段中，事件从目标元素开始向上冒泡，直到最外层的元素。



<h1 id="m4Xla">js事件轮询机制</h1>
<h3 id="N0xUn">讲概念</h3>
JavaScript 事件轮询（Event Loop）是一种异步编程模型，用于处理 JavaScript 中的事件和回调函数。JavaScript 事件轮询机制可以使得单线程的 JavaScript 能够处理多个任务，从而实现异步编程。



<h3 id="jwNn5">详细答</h3>
在浏览器中，JavaScript 事件轮询机制由浏览器的事件循环（Event Loop）负责执行。事件循环是一种机制，它会不断地轮询任务队列（Task Queue），并将队列中的任务依次执行。

JavaScript 中的任务可以分为两类：**宏任务（Macro Task）**和 **微任务（Micro Task）。**

宏任务通常包括一些需要花费较长时间的操作，例如定时器、事件回调等等。当一个宏任务执行完毕后，JavaScript 引擎会检查是否存在未执行的微任务，如果存在，则立即执行这些微任务。在所有微任务执行完毕后，JavaScript 引擎会再次开始执行宏任务。

微任务通常包括一些需要尽快执行的操作，例如 Promise 的回调函数、MutationObserver 的回调函数等等。微任务可以使用 Promise 对象的 then() 方法或者 MutationObserver 的 observe() 方法注册。

在 JavaScript 中，事件轮询机制的执行顺序如下：

1. 执行当前宏任务中的同步代码，直到遇到第一个宏任务或微任务。
2. 如果遇到微任务，则将它添加到微任务队列中，继续执行下一个同步代码。
3. 如果遇到宏任务，则将它添加到宏任务队列中，继续执行下一个同步代码。
4. 当前微任务或者宏任务执行完毕后，JavaScript 引擎会检查微任务或宏任务队列是否为空。如果不为空，则执行队列中的第一个任务，重复执行该步骤直到任务队列为空。
5. 当前事件轮询结束，等待下一次事件的触发。

需要注意的是，JavaScript 中的事件轮询机制是单线程的，也就是说，所有任务都是在同一个线程中执行的，不能同时执行两个任务。如果当前宏任务执行的时间太长，会阻塞其它宏任务的执行，从而导致应用程序的性能问题。因此，在编写 JavaScript 代码时，应该尽可能避免长时间的同步操作，而是使用异步操作，以保证应用程序的性能和响应速度。



<h1 id="wGWsm">解释一下原型链</h1>
<h3 id="e9lLz">讲概念</h3>
原型链是JavaScript中实现继承的一种机制。每个实例对象（object）都有一个私有属性（称之为 __**proto__** ）指向它的构造函数的原型对象（prototype）。该原型对象也有一个自己的原型对象（**__proto__**），层层向上直到一个对象的原型对象为 null 。这样就形成了一个原型链。



<h3 id="v8NXI">可以直接拓展</h3>
如果在当前对象中查找某个属性或方法时，当前对象不存在该属性或方法，JavaScript 引擎会沿着原型链向上查找，直到找到该属性或方法为止，或者查找到原型链的顶端。  


<h3 id="vgLgw">案例理解</h3>
例如，假设有一个对象 obj，它的原型对象为 proto，而 proto 的原型对象为 proto2，那么在查找 obj 的属性或方法时，JavaScript 引擎会按照以下顺序进行查找：

1. 在 obj 本身中查找该属性或方法。
2. 如果 obj 中不存在该属性或方法，则在 proto 中查找。
3. 如果 proto 中也不存在该属性或方法，则继续在 proto2 中查找，以此类推，直到查找到 Object.prototype。
4. 如果在 Object.prototype 中仍然没有找到该属性或方法，则返回 undefined。

这个查找过程就构成了原型链。在 JavaScript 中，对象的原型对象可以通过 __proto__ 属性或 Object.getPrototypeOf() 方法获取。

原型链的作用在于实现了 JavaScript 中的继承。当一个对象需要继承另一个对象的属性和方法时，可以将父对象设置为子对象的原型对象，从而使子对象能够沿着原型链访问父对象的属性和方法。



<h3 id="D8nL3">缺点</h3>
在原型链上查找属性比较耗时，对性能有副作用，这在**性能要求苛刻（一般情况基本无影响）**的情况下很重要。另外，**试图访问不存在的属性时会遍历整个原型链**（这点也很重要，需要记住）。



<h1 id="PhZOJ">谈谈你对作用域的理解</h1>
<h3 id="w5dlu">讲概念</h3>
作用域是指程序代码中定义变量的区域，它规定了变量的可见性和生命周期。在 JavaScript 中，作用域可以分为全局作用域和函数作用域两种，它们之间存在嵌套关系，也就是所谓的作用域链。



<h3 id="usVTB">详细来说  
</h3>
**全局作用域**是指在程序中定义在最外层的变量，它在整个程序的任何位置都可以被访问到。**函数作用域**是指在函数中定义的变量，它们只能在该函数内部被访问到，函数外部无法访问。在函数中定义的变量也可以被嵌套在其他函数的作用域中，这样就形成了作用域链。

当程序执行到一个作用域时，它会先搜索该作用域中的变量，如果找到了就直接使用，否则就向上一级作用域继续搜索，直到找到全局作用域为止。这个搜索过程形成了作用域链，它保证了变量在正确的作用域中被访问和使用。



<h3 id="xONx2">细节需要注意</h3>
JavaScript 中的作用域还有一个特殊的地方，就是在函数中定义的变量，如果没有使用关键字 var 或 let 等声明，它就会变成全局变量。这种情况下，该变量会被添加到全局对象中，可以在任何作用域中访问和使用。

<h3 id="cKFL5">可说可不说</h3>
总之，作用域是 JavaScript 中非常重要的一个概念，它可以控制变量的可见性和生命周期，避免命名冲突和意外修改变量值等问题。理解作用域和作用域链的原理，有助于我们编写出更加健壮和可维护的程序。



<h1 id="MkB6O">闭包的作用和原理以及使用场景</h1>
闭包是指在一个函数内部定义的函数，并且该函数可以访问外部函数的变量和参数。在 JavaScript 中，由于函数是一等对象，因此可以将函数作为返回值，从而形成闭包。



<h4 id="F2Tba">讲概念</h4>
闭包是指有权访问另一个函数作用域中变量的函数，优点是私有化数据，在私有化数据的基础上保持数据，缺点使用不恰当会导致内存泄漏，在不需要用到的时候及时把变量置为null



<h4 id="hJW72">详细来说：</h4>
闭包可以将变量和函数私有化，从而避免命名冲突和变量污染。当函数执行完毕后，该函数内部定义的变量和函数仍然存在于内存中，不会被自动回收，因此可以被其他函数继续访问和使用。这个机制称为闭包。

闭包的原理是在内存中创建一个包含函数和变量的环境，当函数返回后，该环境仍然存在于内存中，因此可以被其他函数访问和使用。闭包中的变量和函数可以被多次调用和修改，因此可以实现许多高级功能。



<h4 id="mZqzM">闭包的使用场景：</h4>
闭包的应用非常广泛，特别是在异步编程和模块化开发中。以下是一些常见的使用场景：

1. 保存变量状态和私有化变量和函数。
2. 用于事件处理和回调函数。
3. 用于封装类和模块。
4. 用于实现柯里化和函数式编程。
5. 用于解决循环中异步问题。
6. 用于实现缓存和记忆化等功能。

  


<h4 id="k0Jo4">注意事项</h4>
需要注意的是，闭包会占用内存并且容易造成内存泄漏，因此在使用闭包时需要注意内存管理和性能优化等问题。



<h3 id="qKwXb">拓展</h3>
<h4 id="f5g0e">闭包一定会造成内存泄露吗</h4>
不是所有闭包都会造成内存泄漏，只有在不正确使用闭包的情况下才会发生内存泄漏。

当一个函数返回一个内部函数，而该内部函数持有外部函数的变量时，就会形成闭包。如果该内部函数在外部函数执行结束后仍然存在，那么它会一直持有外部函数的变量，导致这些变量无法被垃圾回收器回收，从而造成内存泄漏。

例如，以下代码就存在内存泄漏的风险：

```javascript
function outer() {
  var count = 0;
  return function inner() {
    count++;
    console.log(count);
  }
}

var f = outer();
f(); // 1f(); // 2
```



在这个例子中，outer() 函数返回一个内部函数 inner()，而 inner() 持有了 outer() 函数的变量 count。如果 inner() 函数一直存在，count 变量就无法被释放，从而导致内存泄漏。

为了避免内存泄漏，我们可以手动解除闭包，即将对内部函数的引用删除。例如，可以将内部函数赋值为 null，或者将外部函数返回的引用赋值为 null。

总之，闭包不一定会造成内存泄漏，只有在不正确使用闭包的情况下才会发生内存泄漏。在编写代码时，需要注意正确使用闭包并避免内存泄漏的问题。



<h4 id="G1SlZ">使用哪些方式可以防止闭包引起的内存泄漏</h4>
以下是一些可以防止闭包引起内存泄漏的方式：

1. 避免创建不必要的闭包：如果闭包中包含的变量在函数执行完后不再需要使用，可以避免创建闭包，从而避免内存泄漏的问题。
2. 及时释放闭包：在使用闭包时，需要在不需要时及时释放闭包，可以使用变量赋值为 null 或者手动解除对闭包的引用等方式来释放闭包。
3. 使用模块模式：在模块模式中，可以使用立即执行函数（IIFE）来创建一个私有作用域，从而避免闭包中的变量被外部访问，避免了内存泄漏的问题。
4. 避免循环引用：如果闭包中引用了 DOM 元素或其他对象，需要确保在不需要时及时释放这些对象，避免循环引用造成内存泄漏的问题。

总之，防止闭包引起内存泄漏需要结合具体的情况进行综合考虑和处理，同时也需要注意代码的可读性和可维护性。  


<h1 id="Qa8TH">js连续多个bind，最后this指向是什么？</h1>
<h4 id="Vmty1">点题收敛</h4>
指向第一个绑定的对象



<h4 id="zpsqF">详细来说</h4>
在 JavaScript 中，可以使用 bind() 方法来改变函数执行时的 this 上下文。如果连续多次对同一个函数使用 bind() 方法，那么每次调用 bind() 方法都会返回一个新的函数，但是这些新函数的 this 上下文都是相同的，指向第一个绑定的对象



<h4 id="McgKe">案例理解</h4>
例如，以下代码连续调用了三次 bind() 方法：

```javascript
function foo() {
 console.log(this.name);
}
const obj1 = { name: 'object 1' };
const obj2 = { name: 'object 2' };
const bound1 = foo.bind(obj1).bind(obj2).bind(null);
bound1(); // 输出: "object 1"
```



在这个例子中，我们先将 foo 函数绑定到 obj1 对象上，然后再将返回的新函数绑定到 obj2 对象上，最后再将返回的新函数绑定到 null 上。因为 null 是无效的对象，所以最终的函数中的 this 指向的是第一个 bind() 方法绑定的 obj1 对象，所以输出的结果是 "object 1"。

需要注意的是，如果在使用 bind() 方法时，最终绑定的对象为 null 或 undefined，那么 this 上下文会指向全局对象 window（浏览器环境）或 global（Node.js 环境），这可能会导致不可预期的行为。



<h1 id="fRjF2">0.1+0.2为什么不等于0.3</h1>
<h4 id="MwtVF">核心</h4>
在 JavaScript 中，浮点数是以 IEEE 754 标准的二进制浮点数表示的，它采用二进制的形式来表示实数。而二进制无法精确地表示某些十进制小数，例如 0.1 和 0.2，因为它们在二进制下是无限循环的小数，而浮点数只有 64 位的精度。

因此，当在 JavaScript 中执行 0.1 + 0.2 的计算时，由于无法精确表示这两个数字，它们会被转换成最接近的可表示二进制数，然后再进行计算。这会导致一个微小的舍入误差，使得结果不等于 0.3。  


<h4 id="t7aIs">如何解决</h4>
可以使用 toFixed() 方法将结果四舍五入到指定小数位数，例如：

```javascript
const sum = 0.1 + 0.2; // 0.30000000000000004
const roundedSum = sum.toFixed(1); // "0.3"console.log(roundedSum === "0.3"); // true
```

<font style="color:rgb(51, 51, 51);"></font>

需要注意的是，toFixed() 方法返回一个字符串类型的结果，因此需要注意类型转换。



<h1 id="jrKzq">symbol这个新增的基础数据类型有什么用</h1>
<h4 id="MhpE9">讲概念点题</h4>
Symbol 是在 ES6 中新增的基础数据类型，它的主要作用是创建一个唯一的标识符，用于对象属性名的命名、常量的定义等场景。  


<h4 id="wvyWX">案例理解</h4>
每个 Symbol 都是唯一的，可以用作对象的属性名，这样就可以避免属性名冲突的问题。例如：

```javascript
const s1 = Symbol();
const s2 = Symbol();
const obj = {
 [s1]: 'hello',
 [s2]: 'world'
};
console.log(obj[s1]); // "hello"console.log(obj[s2]); // "world"
```



另外，Symbol 还可以用于实现一些常量或枚举值，这些值是不可修改和重复的，例如：

```javascript
const Colors = {
 Red: Symbol('Red'),
 Green: Symbol('Green'),
 Blue: Symbol('Blue')
};
console.log(Colors.Red); // Symbol(Red)console.log(Colors.Green); // Symbol(Green)console.log(Colors.Blue); // Symbol(Blue)
```



由于 Symbol 是一种基础数据类型，所以它具有很高的性能和可靠性，可以用于需要高效和安全的标识符创建和使用的场景。



<h4 id="ROmfn">注意事项</h4>
Symbol 还有一个重要的特点是，它不会出现在 for...in、for...of、Object.keys()、Object.getOwnPropertyNames() 等遍历对象属性的方法中，因此可以用来定义一些不希望被遍历到的属性，例如一些内部实现细节或隐藏属性



<h1 id="bkUKn">typeof null 为什么是object</h1>
<h4 id="HcYGo">核心</h4>
typeof null 的结果为 "object"，这是 JavaScript 语言的一个历史遗留问题。

在 JavaScript 最初的版本中，使用 32 位的值表示一个变量，其中前 3 位用于表示值的类型。000 表示对象，010 表示浮点数，100 表示字符串，110 表示布尔值，和其他的值都被认为是指针。

在这种表示法下，null 被解释为一个全零的指针，也就是说它被认为是一个空对象引用，因此 typeof null 的结果就是 "object"。



<h4 id="OxBwY">拓展</h4>
虽然这个设计是一个历史遗留问题，但是由于历史原因，已经成为 JavaScript 语言的一部分，无法修复。因此在判断变量是否为 null 时，建议使用严格相等运算符（===）进行判断。

<h1 id="CSmPA">es6新特性？箭头函数和普通函数有啥区别？箭头函数能当构造函数吗？</h1>
ES6（ECMAScript 2015）是 JavaScript 的一个新版本，引入了很多新的特性和语法，其中一些比较常用的包括：

1. **块级作用域：**通过 let 和 const 声明的变量只在当前块级作用域中有效。
2. **箭头函数：**使用 => 符号定义的函数，具有简化的语法和自动绑定 this 上下文的特点。
3. **模板字符串：**使用反引号 `` 和 ${} 操作符，可以方便地拼接字符串和变量。
4. **解构赋值：**可以将数组或对象的值解构赋给变量。
5. **类和继承：**引入了 class 和 extends 关键字，使得 JavaScript 支持面向对象编程。
6. **Promise 和 async/await：**用于处理异步编程的新特性。



关于箭头函数和普通函数的区别，主要有以下几点：

1. 箭头函数没有自己的 this 上下文，它的 this 上下文继承自外部作用域，因此不能使用 call()、apply() 或 bind() 方法改变 this 上下文。
2. 箭头函数没有自己的 arguments 对象，如果需要获取函数参数，可以使用 rest 参数或者展开运算符。
3. 箭头函数不能作为构造函数使用，不能使用 new 关键字创建对象。



<h5 id="n3ZXC">注意</h5>
**关于箭头函数能否作为构造函数的问题，根据规范来说，箭头函数是没有 [[Construct]] 方法的，因此不能使用 new 关键字创建对象**。如果强制使用 new 关键字调用箭头函数，会抛出一个类型错误。因此，一般来说箭头函数不应该用于创建对象，而应该用于函数式编程和简化回调函数等场景。

<h1 id="wQM8H">promise.all 和 promise.allsettled区别</h1>
Promise.all() 和 Promise.allSettled() 是两个 JavaScript Promise 相关的方法，它们都用于处理多个 Promise 对象的并发执行。但是它们之间有一些区别。

1. Promise.all() 方法返回的 Promise 对象在所有的 Promise 对象都 resolve 之后，才会 resolve 并返回一个由所有 Promise 返回值组成的数组。如果其中有一个 Promise 被 reject，则会立即 reject 并返回相应的错误信息。简单来说，只有所有 Promise 都成功了才算成功，有一个失败了就算失败。
2. Promise.allSettled() 方法返回的 Promise 对象在所有的 Promise 对象都 resolve 或 reject 之后，才会 resolve 并返回一个由所有 Promise 状态对象组成的数组，每个状态对象包含一个 status 字段表示 Promise 状态，和一个 value 或 reason 字段表示 Promise 的返回值或错误信息。简单来说，它会等到所有 Promise 都执行完毕，无论成功还是失败，都会把每个 Promise 的状态信息收集到一个数组里面返回。

因此，Promise.all() 方法在需要所有 Promise 都成功的情况下使用，而 Promise.allSettled() 方法则适用于需要知道每个 Promise 的执行结果的情况下使用。


