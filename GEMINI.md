根据我提供的instruction.md 和docs 在cards中生成docs对应的卡片集
已经在cards中生成的内容,就不用管了
在生成卡片的deck时候你需要参考cardtree.md文件,如果根据文章内容生成的deck,并没有在树中出现,那么你需要在cardtree.md中的树中添加
生成完,一定要检查一下,尤其是:对于 `AnkiNexus` 卡片，此列为包含链接信息的 JSON 字符串。格式为：`"[{""title"": ""...", ""deck"": ""..."", ""note_guid"": ""...""}]"`, 这里的格式很容易出错

根据我提供的instruction.md 和vue高频面试题.md 生成对应的卡片,在生成卡片的deck时候你需要参考cardtree.md文件,如果根据文章内容生成的deck,并没有在树中出现,那么你需要在cardtree.md中的树中添加,生成完,一定要检查一下,尤其是:1.对于 `AnkiNexus` 卡片，此列为包含链接信息的 JSON 字符串。格式为：`"[{""title"": ""...", ""deck"": ""..."", ""note_guid"": ""...""}]"` 2.note_guid不能以#号开头,这里的格式很容易出错 3.双引号"被用与囊括换行段落,如果文本内容本身需要双引号使用单引号代替

