#separator:tab
#html:true
#guid column:1
#notetype column:2
#deck column:3
#tags column:7
VUE_CORE_UNDERSTANDING_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::核心思想	谈谈你对Vue.js的理解，包含哪些核心方面？	"<ul><li>Vue.js的定位和核心思想</li><li>Vue.js的主要优势</li><li>Vue.js的响应式数据绑定机制</li></ul>"	"[{""title"": ""Vue.js是什么？它的定位是什么？"", ""deck"": ""前端::7-框架与生态::Vue::核心思想"", ""note_guid"": ""VUE_DEFINITION""}, {""title"": ""Vue.js的核心思想是什么？"", ""deck"": ""前端::7-框架与生态::Vue::核心思想"", ""note_guid"": ""VUE_CORE_IDEAS""}, {""title"": ""Vue.js有哪些主要优势？"", ""deck"": ""前端::7-框架与生态::Vue::核心思想"", ""note_guid"": ""VUE_ADVANTAGES""}, {""title"": ""Vue.js的响应式数据绑定机制是如何工作的？"", ""deck"": ""前端::7-框架与生态::Vue::响应式原理"", ""note_guid"": ""VUE_REACTIVITY_MECHANISM""}]"	Vue 核心思想
VUE_DEFINITION	基础	前端::7-框架与生态::Vue::核心思想	Vue.js是什么？它的定位是什么？	Vue.js是一个<b>渐进式JavaScript框架</b>，它专注于构建用户界面（UI）。	Vue 核心思想
VUE_CORE_IDEAS	基础	前端::7-框架与生态::Vue::核心思想	Vue.js的核心思想是什么？	Vue的核心思想是<b>数据驱动</b>和<b>组件化</b>。通过将页面拆分成独立的组件，可以更好地管理和复用代码。	Vue 核心思想 组件化
VUE_ADVANTAGES	基础	前端::7-框架与生态::Vue::核心思想	Vue.js有哪些主要优势？	"<ul><li><b>简单易用</b>：模板语法易于学习和理解。</li><li><b>灵活性高</b>：可以满足各种复杂需求。</li><li><b>性能卓越</b>：通过异步DOM更新和虚拟DOM提升性能。</li><li><b>扩展性强</b>：拥有如Vuex、Vue Router等丰富的官方插件生态。</li></ul>"		Vue 核心思想
VUE_REACTIVITY_MECHANISM	基础	前端::7-框架与生态::Vue::响应式原理	Vue.js的响应式数据绑定机制是如何工作的？	Vue通过对数据进行<b>劫持和监听</b>（在Vue 2中使用`Object.defineProperty`，Vue 3中使用`Proxy`）来实现双向绑定。当数据发生变化时，视图会自动更新；反之，视图的变化也会同步到数据上，使得数据流清晰可预测。	Vue 响应式原理
VUE_NEXTTICK_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::核心思想	关于Vue.nextTick()，你需要了解哪些？	"<ul><li>`Vue.nextTick()`的用途是什么？</li><li>`Vue.nextTick()`的实现原理是什么？</li><li>`Vue.nextTick()`的具体实现方式有哪些？</li></ul>"	"[{""title"": ""在Vue中，`nextTick()`的主要用途是什么？"", ""deck"": ""前端::7-框架与生态::Vue::核心思想"", ""note_guid"": ""VUE_NEXTTICK_PURPOSE""}, {""title"": ""Vue.nextTick() 的核心实现原理是什么？"", ""deck"": ""前端::7-框架与生态::Vue::核心思想"", ""note_guid"": ""VUE_NEXTTICK_PRINCIPLE""}, {""title"": ""Vue.nextTick() 在不同浏览器环境下有哪些具体的实现方式？"", ""deck"": ""前端::7-框架与生态::Vue::核心思想"", ""note_guid"": ""VUE_NEXTTICK_IMPLEMENTATIONS""}]"	Vue nextTick
VUE_NEXTTICK_PURPOSE	基础	前端::7-框架与生态::Vue::核心思想	在Vue中，`nextTick()`的主要用途是什么？	"当我们在Vue中修改数据后，DOM并不会立即更新，而是异步执行。`nextTick()`的用途就是提供一个回调函数，这个函数会在<b>下一次DOM更新循环结束之后</b>执行。这确保了我们能操作到更新后的DOM。<b>关键点：</b>确保我们操作的是更新后的 DOM；这样做可以避免频繁的 DOM 操作，提高性能。"	Vue nextTick
VUE_NEXTTICK_PRINCIPLE	基础	前端::7-框架与生态::Vue::核心思想	Vue.nextTick() 的核心实现原理是什么？	`Vue.nextTick()`的实现原理是基于浏览器的<b>异步任务队列</b>，并采用<b>微任务优先</b>的策略。当数据修改时，Vue将DOM更新操作推入一个异步任务队列。`nextTick()`则将一个回调函数也推入该队列，以确保它在DOM更新操作之后执行。	Vue nextTick EventLoop
VUE_NEXTTICK_IMPLEMENTATIONS	基础	前端::7-框架与生态::Vue::核心思想	Vue.nextTick() 在不同浏览器环境下有哪些具体的实现方式？	"Vue会根据浏览器环境选择最优的异步方式，优先级如下：<ol><li><b>Promise.then()</b> (微任务)</li><li><b>MutationObserver</b> (微任务)</li><li><b>setImmediate</b> (宏任务，仅IE)</li><li><b>setTimeout(fn, 0)</b> (宏任务，最后的备选方案)</li></ol>"		Vue nextTick EventLoop
VUE_LIST_KEY_REASON	基础	前端::7-框架与生态::Vue::渲染机制	为什么在Vue渲染列表时，不建议使用数组的下标(index)作为key值？	"因为key是Vue用来识别节点、进行高效diff算法的核心依据。如果使用index作为key：<br>1. <b>性能问题</b>：当在数组<b>非末尾</b>插入或删除元素时，后面的所有元素的index都会改变。这会导致Vue认为这些都是'新'节点，从而触发大量不必要的组件重新渲染或DOM操作，而不是简单地移动现有元素。<br>2. <b>状态混乱</b>：如果列表项包含有自身状态的组件（如输入框），使用index作为key可能导致更新后，组件的状态与数据错位。例如，删除了第一项，但第二项的输入框内容却留在了现在的第一项位置上。<br><br><b>最佳实践</b>：key应该是<b>唯一的、稳定的、与内容绑定的</b>值，如列表中每项数据的`id`。"	Vue 渲染机制 diff key
VUEX_UNDERSTANDING_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::生态-Router-Pinia	谈谈你对Vuex的理解，包含哪些核心方面？	"<ul><li>Vuex的定义和核心思想</li><li>Vuex的核心概念</li><li>Vuex的数据流向</li></ul>"	"[{""title"": ""Vuex是什么？它的核心思想是什么？"", ""deck"": ""前端::7-框架与生态::Vue::生态-Router-Pinia"", ""note_guid"": ""VUEX_DEFINITION""}, {""title"": ""Vuex的四大核心概念是什么？"", ""deck"": ""前端::7-框架与生态::Vue::生态-Router-Pinia"", ""note_guid"": ""VUEX_CORE_CONCEPTS""}, {""title"": ""Vuex的数据流是怎样的？"", ""deck"": ""前端::7-框架与生态::Vue::生态-Router-Pinia"", ""note_guid"": ""VUEX_DATA_FLOW""}]"	Vue Vuex
VUEX_DEFINITION	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	Vuex是什么？它的核心思想是什么？	Vuex是一个专为Vue.js开发的<b>状态管理库</b>。其核心思想是将所有组件的共享状态抽离出来，以一个<b>集中式的、单一的状态树(Single Source of Truth)</b>进行管理。	Vue Vuex 状态管理
VUEX_CORE_CONCEPTS	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	Vuex的四大核心概念是什么？	"<ul><li><b>State</b>：驱动应用的数据源，即应用的状态。</li><li><b>Mutations</b>：<b>唯一</b>可以同步修改State的地方。</li><li><b>Actions</b>：用于处理<b>异步操作</b>或批量的同步操作，它不能直接修改State，而是通过提交(commit) Mutations来改变。</li><li><b>Getters</b>：从State中派生出一些状态，可以看作是store的计算属性。</li></ul>"		Vue Vuex
VUEX_DATA_FLOW	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	Vuex的数据流是怎样的？	Vuex遵循<b>单向数据流</b>：<br>1. 视图(Component)通过`dispatch`一个Action来触发异步操作或逻辑。<br>2. Action通过`commit`一个Mutation来请求状态变更。<br>3. Mutation是唯一修改State的地方。<br>4. State的变更会通过响应式系统自动更新到所有订阅了该状态的视图。	Vue Vuex
VUE_ROUTER_HOOKS_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::生态-Router-Pinia	Vue Router有哪几种导航钩子？	"Vue Router的导航钩子可以分为三类：<ol><li>全局守卫</li><li>路由独享守卫</li><li>组件内守卫</li></ol>"	"[{""title"": ""Vue Router的全局导航守卫有哪些？"", ""deck"": ""前端::7-框架与生态::Vue::生态-Router-Pinia"", ""note_guid"": ""VUE_ROUTER_GLOBAL_GUARDS""}, {""title"": ""Vue Router的路由独享守卫是什么？"", ""deck"": ""前端::7-框架与生态::Vue::生态-Router-Pinia"", ""note_guid"": ""VUE_ROUTER_PER_ROUTE_GUARD""}, {""title"": ""Vue Router的组件内守卫有哪些？"", ""deck"": ""前端::7-框架与生态::Vue::生态-Router-Pinia"", ""note_guid"": ""VUE_ROUTER_IN_COMPONENT_GUARDS""}]"	Vue VueRouter
VUE_ROUTER_GLOBAL_GUARDS	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	Vue Router的全局导航守卫有哪些？	"<ul><li>`beforeEach`: 在每次路由跳转<b>之前</b>执行，常用于身份验证、路由拦截。</li><li>`beforeResolve`: 在导航被确认之前，同时在所有组件内守卫和异步路由组件被解析之后执行。</li><li>`afterEach`: 在每次路由跳转<b>之后</b>执行，常用于页面统计(PV)等。</li></ul>"		Vue VueRouter
VUE_ROUTER_PER_ROUTE_GUARD	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	Vue Router的路由独享守卫是什么？	路由独享守卫是直接在路由配置上定义的`beforeEnter`守卫。它只在<b>进入</b>对应路由时触发，与`beforeEach`的区别在于它只作用于当前路由。	Vue VueRouter
VUE_ROUTER_IN_COMPONENT_GUARDS	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	Vue Router的组件内守卫有哪些？	"<ul><li>`beforeRouteEnter`: 在渲染该组件的对应路由被确认<b>前</b>调用。<b>不能</b>获取组件实例 `this`，因为当守卫执行前，组件实例还没被创建。</li><li>`beforeRouteUpdate`: 在当前路由改变，但是该组件被复用时调用。例如，对于一个带有动态参数的路径 `/user/:id`，在 `/user/1` 和 `/user/2` 之间跳转时。</li><li>`beforeRouteLeave`: 导航离开该组件的对应路由时调用。通常用来禁止用户在还未保存修改前离开。</li></ul>"		Vue VueRouter
VUE_ROUTER_PRINCIPLE_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::生态-Router-Pinia	Vue Router的核心原理是什么？	"Vue Router的核心原理主要包括：<ol><li>路由模式（Hash vs History）</li><li>URL监听与路由匹配</li><li>导航控制（导航守卫）</li><li>组件渲染</li></ol>"	"[{""title"": ""Vue Router的两种路由模式（Hash vs History）有什么区别？"", ""deck"": ""前端::7-框架与生态::Vue::生态-Router-Pinia"", ""note_guid"": ""VUE_ROUTER_MODES_DIFF""}, {""title"": ""Vue Router是如何监听URL变化并匹配相应组件的？"", ""deck"": ""前端::7-框架与生态::Vue::生态-Router-Pinia"", ""note_guid"": ""VUE_ROUTER_MATCHING""}]"	Vue VueRouter
VUE_ROUTER_MODES_DIFF	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	Vue Router的两种路由模式（Hash vs History）有什么区别？	"<ul><li><b>Hash模式</b>：URL中带有`#`符号（如`example.com/#/user`）。原理是监听`hashchange`事件。对服务端友好，因为`#`后的内容不会被发送到服务器，刷新页面不会出问题，只需要前端处理。</li><li><b>History模式</b>：URL更美观，没有`#`（如`example.com/user`）。原理是利用HTML5 History API（`pushState`, `replaceState`）和监听`popstate`事件。<b>缺点</b>是刷新页面或直接访问URL时，浏览器会向服务器请求该路径，如果服务器没有对应的处理，会返回404。因此需要<b>后端配置支持</b>，将所有请求都重定向到主入口文件（如`index.html`）。</li></ul>"		Vue VueRouter
VUE_ROUTER_MATCHING	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	Vue Router是如何监听URL变化并匹配相应组件的？	Vue Router内部维护一个路由表（一个从路径到组件的映射）。当监听到URL变化后，它会遍历这个路由表，找到与当前URL匹配的路由记录，然后根据记录中的组件定义，使用Vue的动态组件机制（`<component :is='...'>`）和`<router-view>`占位符，将匹配到的组件渲染到正确的位置。	Vue VueRouter
VUE_ROUTER_HISTORY_404	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	为什么Vue Router的History模式在刷新页面时会出现404错误？	因为在History模式下，URL看起来像一个真实的服务器路径。当用户刷新页面或直接访问这个URL时，浏览器会向服务器发送一个对该路径的GET请求。由于这是一个单页应用（SPA），服务器上并没有与该路径对应的物理文件或资源，因此服务器会返回404 Not Found。	Vue VueRouter History
VUE_ROUTER_HISTORY_404_SOLUTION	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	如何解决Vue Router History模式刷新页面时的404问题？	"需要在<b>服务器端</b>进行配置，将所有未匹配到静态资源的请求都重定向（fallback）到应用的入口文件（通常是`index.html`）。这样，应用就会被重新加载，然后Vue Router接管路由，根据URL显示正确的页面。<br><br><b>Nginx配置示例:</b><pre><code>server {
    ...
    location / {
        try_files $uri $uri/ /index.html;
    }
}</code></pre>"	Vue VueRouter History
VUE_ROUTER_HASH_ANCHOR	基础	前端::7-框架与生态::Vue::生态-Router-Pinia	如何使用Vue Router的Hash模式实现页面内锚点跳转？	"在Hash模式下，URL的第一个`#`用于路由，第二个`#`才能被浏览器识别为锚点。因此，直接使用`path: '/path#anchor'`是无效的。<br><br><b>正确实现方式：</b><br>1. 使用`router.push('/path')`跳转到目标页面。<br>2. 在目标页面的`mounted`钩子中，通过`this.$route.hash`获取锚点值（例如`#youranchor`），然后使用`document.getElementById()`找到对应元素，并调用其`scrollIntoView()`方法。<br><pre><code>mounted() {
  if (this.$route.hash) {
    const anchor = document.getElementById(this.$route.hash.slice(1));
    if (anchor) {
      anchor.scrollIntoView();
    }
  }
}</code></pre>"	Vue VueRouter Hash 锚点
VIRTUAL_DOM_DIFF_KEY_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::渲染机制	关于虚拟DOM(Virtual DOM)、diff算法和key的作用，你需要了解什么？	"<ul><li>什么是虚拟DOM？</li><li>什么是diff算法？</li><li>key在diff算法中扮演什么角色？</li></ul>"	"[{""title"": ""什么是虚拟DOM (Virtual DOM)？它解决了什么问题？"", ""deck"": ""前端::7-框架与生态::Vue::渲染机制"", ""note_guid"": ""VIRTUAL_DOM_DEFINITION""}, {""title"": ""在Vue/React中，diff算法的基本工作原理是什么？"", ""deck"": ""前端::7-框架与生态::Vue::渲染机制"", ""note_guid"": ""DIFF_ALGORITHM_PRINCIPLE""}, {""title"": ""在Vue/React的diff算法中，key的作用是什么？"", ""deck"": ""前端::7-框架与与生态::Vue::渲染机制"", ""note_guid"": ""VUE_LIST_KEY_REASON""}]"	Vue React 虚拟DOM diff
VIRTUAL_DOM_DEFINITION	基础	前端::7-框架与生态::Vue::渲染机制	什么是虚拟DOM (Virtual DOM)？它解决了什么问题？	"<b>虚拟DOM</b>是一个用JavaScript对象来模拟真实DOM树结构的轻量级副本。它包含了节点的类型、属性和子节点等信息。<br><br><b>核心解决的问题</b>：避免直接、频繁地操作真实DOM带来的巨大性能开销。通过在内存中比较新旧两个虚拟DOM树的差异（使用diff算法），计算出最小的变更集，然后才将这些变更<b>一次性</b>地应用到真实DOM上。"	Vue React 虚拟DOM
DIFF_ALGORITHM_PRINCIPLE	基础	前端::7-框架与生态::Vue::渲染机制	在Vue/React中，diff算法的基本工作原理是什么？	"Diff算法是一种在两个树形结构间找出差异的算法。在Vue/React中，它遵循三个核心策略来简化比较，将O(n^3)的复杂度降低到O(n)：<br>1. <b>只在同层级进行比较</b>：不会跨层级移动节点。如果一个节点在不同层级出现，它会被销毁并重建。<br>2. <b>不同类型的节点会直接替换</b>：如果一个`<div>`变成了`<p>`，React/Vue会销毁旧的`<div>`及其子树，并创建新的`<p>`。<br>3. <b>通过key来识别相同类型的兄弟节点</b>：对于同层级的一组子节点，通过key来快速判断哪些节点是新增、删除或移动的。"	Vue React diff
VUE2_VUE3_DIFF_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::架构与设计	Vue 2 和 Vue 3 有哪些主要区别？	"<ul><li>核心API：Composition API vs Options API</li><li>响应式原理</li><li>性能提升</li><li>TypeScript支持</li><li>Tree Shaking支持</li><li>其他新特性</li></ul>"	"[{""title"": ""Vue 3引入的Composition API与Vue 2的Options API相比，主要解决了什么问题？"", ""deck"": ""前端::7-框架与生态::Vue::架构与设计"", ""note_guid"": ""VUE3_COMPOSITION_API_VS_OPTIONS_API""}, {""title"": ""Vue 3和Vue 2在响应式原理上有什么根本区别？"", ""deck"": ""前端::7-框架与生态::Vue::响应式原理"", ""note_guid"": ""VUE3_VS_VUE2_REACTIVITY""}, {""title"": ""相比Vue 2，Vue 3在性能上做了哪些主要优化？"", ""deck"": ""前端::7-框架与生态::Vue::性能与用户体验"", ""note_guid"": ""VUE3_PERFORMANCE_OPTIMIZATIONS""}]"	Vue2 Vue3
VUE3_COMPOSITION_API_VS_OPTIONS_API	基础	前端::7-框架与生态::Vue::架构与设计	Vue 3引入的Composition API与Vue 2的Options API相比，主要解决了什么问题？	"<b>Options API</b> (Vue 2) 的问题是，当组件变得复杂时，同一个逻辑关注点的代码（如数据、方法、计算属性）被分割在不同的选项块中，难以阅读和维护。同时，逻辑复用依赖于Mixins，容易导致命名冲突和数据来源不清晰。<br><br><b>Composition API</b> (Vue 3) 解决了这个问题，它允许我们将<b>围绕同一个逻辑关注点的代码组织在一起</b>。这使得代码更具可读性，并且可以轻松地将这些逻辑提取为可复用的组合式函数(Composables)，提供了比Mixins更清晰、更灵活的逻辑复用机制。"	Vue3 CompositionAPI
VUE3_VS_VUE2_REACTIVITY	基础	前端::7-框架与生态::Vue::响应式原理	Vue 3和Vue 2在响应式原理上有什么根本区别？	"<ul><li><b>Vue 2</b>: 基于 `Object.defineProperty()`。它通过遍历data对象的所有属性，并为每个属性设置getter/setter来实现响应式。<b>缺点</b>：无法检测到对象属性的动态添加或删除，也无法直接监听数组索引的变化，需要通过`Vue.set`等API辅助。</li><li><b>Vue 3</b>: 基于 `Proxy`。它代理整个对象，而不是对象的属性。<b>优点</b>：可以原生地监听到对象属性的动态添加、删除以及数组索引和长度的变化，无需额外API，性能也更好。</li></ul>"	Vue2 Vue3 响应式原理
VUE3_PERFORMANCE_OPTIMIZATIONS	基础	前端::7-框架与生态::Vue::性能与用户体验	相比Vue 2，Vue 3在性能上做了哪些主要优化？	"Vue 3的性能提升是全方位的：<br>1. <b>更快的diff算法</b>：引入了<b>最长递增子序列</b>算法来优化节点移动，并结合<b>静态标记(Patch Flags)</b>，在编译时标记动态内容，diff时跳过静态部分。<br>2. <b>静态内容提升</b>：将模板中的静态节点提升到渲染函数之外，避免每次渲染都重新创建。<br>3. <b>树结构打平</b>：将模板动态节点收集到一个扁平数组中，减少diff的遍历深度。<br>4. <b>更小的打包体积</b>：通过更好的Tree Shaking支持，核心库的依赖更少，可以将未使用的API从最终包中移除。"	Vue3 性能优化 diff
STYLE_SCOPED_REASON	基础	前端::2-CSS	在Vue组件的`<style>`标签中添加`scoped`属性的目的是什么？	"添加`scoped`属性是为了实现<b>样式作用域的局部化</b>。它的目的是确保当前组件的样式<b>只作用于当前组件内的元素</b>，而不会泄露出去影响全局或其他组件的样式。<br><br><b>实现原理</b>：Vue编译器会为组件内的每个元素添加一个唯一的自定义属性（如`data-v-f3f3eg9`），同时改写CSS选择器，使其带上这个属性选择器（如`.example`会变成`.example[data-v-f3f3eg9]`），从而保证了样式的隔离。"	Vue CSS 作用域
VUE2_ARRAY_METHODS_MODIFICATION	基础	前端::7-框架与生态::Vue::响应式原理	Vue 2为了实现数组的响应式更新，重写（或说'增强'）了哪些数组方法？为什么？	"Vue 2重写了以下7个可以<b>改变原数组</b>的方法：<br><ul><li>`push()`</li><li>`pop()`</li><li>`shift()`</li><li>`unshift()`</li><li>`splice()`</li><li>`sort()`</li><li>`reverse()`</li></ul><b>原因</b>：`Object.defineProperty`本身无法监听到数组内容的直接修改（如`arr[0] = 'new value'`）或长度的变化。通过重写这些方法，Vue可以在这些方法被调用时，除了执行原始的数组操作外，还能额外<b>派发更新通知</b>，从而触发视图的重新渲染。"	Vue2 响应式原理
KEEPALIVE_LIFECYCLE_PRIORITY	基础	前端::7-框架与生态::Vue::组件化	对于一个被`<keep-alive>`包裹的组件，`mounted`和`activated`生命周期钩子的执行优先级是怎样的？	"在一个被`<keep-alive>`缓存的组件中：<br>1. <b>首次加载时</b>：组件会被正常创建和挂载，执行顺序是 `... -> created -> mounted`。此时`activated`不会被触发。<br>2. <b>从缓存中激活时</b>：组件不会重新创建和挂载，因此`created`和`mounted`不会再执行。取而代之的是`activated`钩子被触发。<br>3. <b>被移出且缓存时</b>：会触发`deactivated`钩子。<br><br><b>结论</b>：`mounted`只在组件第一次挂载时执行一次。而`activated`在每次组件从缓存中被激活时都会执行。因此，不存在直接的“优先级”比较，它们在组件生命周期的不同阶段起作用。"	Vue keep-alive 生命周期
VUE3_DIFF_ADVANTAGES	基础	前端::7-框架与生态::Vue::渲染机制	Vue 3.0 使用的 diff 算法相比 Vue 2.0 中的双端比对有哪些优势？	"Vue 3的diff算法（基于最长递增子序列）相比Vue 2的双端diff，主要优势在于<b>更高效地处理节点移动</b>。<br><br>1. <b>最长递增子序列 (LIS)</b>：Vue 3首先通过key找到新旧VNode数组中的相同节点，然后计算出新数组中这些节点的索引构成的最长递增子序列。这个子序列中的节点被认为是稳定的，<b>不需要移动</b>。只需要移动那些不在这个子序列中的节点即可。这大大减少了DOM的移动操作，尤其是在列表大量重排序时。<br>2. <b>静态标记 (Patch Flags)</b>：编译器在编译时就标记了节点的动态部分（如动态class、动态style等）。在diff时，Vue 3可以直接根据这些标记进行靶向更新，而无需对整个节点进行完整的属性比对，速度更快。<br>3. <b>缓存和动态删除</b>：利用缓存数组减少比对次数，并异步合并删除操作，减少DOM操作。"	Vue3 Vue2 diff
VUE_PARENT_CHILD_LIFECYCLE_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::组件化	Vue父子组件的生命周期钩子执行顺序是怎样的？	"分为三个主要阶段：<br>1. 加载/挂载阶段<br>2. 更新阶段<br>3. 销毁阶段"	"[{""title"": ""在加载/挂载阶段，Vue父子组件的生命周期钩子执行顺序是？"", ""deck"": ""前端::7-框架与生态::Vue::组件化"", ""note_guid"": ""VUE_LIFECYCLE_MOUNTING""}, {""title"": ""在更新阶段，Vue父子组件的生命周期钩子执行顺序是？"", ""deck"": ""前端::7-框架与生态::Vue::组件化"", ""note_guid"": ""VUE_LIFECYCLE_UPDATING""}, {""title"": ""在销毁阶段，Vue父子组件的生命周期钩子执行顺序是？"", ""deck"": ""前端::7-框架与生态::Vue::组件化"", ""note_guid"": ""VUE_LIFECYCLE_DESTROYING""}]"	Vue 生命周期
VUE_LIFECYCLE_MOUNTING	基础	前端::7-框架与生态::Vue::组件化	在加载/挂载阶段，Vue父子组件的生命周期钩子执行顺序是？	"父 beforeCreate -> 父 created -> 父 beforeMount -> 子 beforeCreate -> 子 created -> 子 beforeMount -> <b>子 mounted</b> -> <b>父 mounted</b><br><br><b>记忆技巧</b>：从外到内，再从内到外。父组件先开始，然后遇到子组件时，需要等待子组件完全挂载（mounted）后，父组件才能宣告自己也挂载完成。"	Vue 生命周期
VUE_LIFECYCLE_UPDATING	基础	前端::7-框架与生态::Vue::组件化	在更新阶段，Vue父子组件的生命周期钩子执行顺序是？	"父 beforeUpdate -> <b>子 beforeUpdate</b> -> <b>子 updated</b> -> 父 updated<br><br><b>记忆技巧</b>：更新过程也是从外到内（beforeUpdate），然后从内到外（updated）。子组件的更新必须先于父组件完成。"	Vue 生命周期
VUE_LIFECYCLE_DESTROYING	基础	前端::7-框架与生态::Vue::组件化	在销毁阶段，Vue父子组件的生命周期钩子执行顺序是？	"父 beforeDestroy -> <b>子 beforeDestroy</b> -> <b>子 destroyed</b> -> 父 destroyed<br><br><b>记忆技巧</b>：销毁过程同样是从外到内（beforeDestroy），然后从内到外（destroyed）。父组件要销毁，必须先等其所有子组件都销毁完成。"	Vue 生命周期
VUE_ARRAY_CHANGE_DETECTION	基础	前端::7-框架与生态::Vue::响应式原理	在Vue中，如何检测数组的变化？	"Vue对数组变化的检测分为两种情况：<br>1. <b>对于调用了被Vue重写的7个数组方法</b> (`push`, `pop`, `shift`, `unshift`, `splice`, `sort`, `reverse`)，Vue能直接监听到变化并更新视图。<br>2. <b>对于通过索引直接修改数组项</b> (e.g., `this.arr[0] = 'newVal'`) 或<b>直接修改数组长度</b> (e.g., `this.arr.length = 0`)，Vue 2<b>无法</b>检测到。必须使用以下方法：<br>   - `Vue.set(this.arr, index, newValue)` 或 `this.$set(...)`<br>   - `this.arr.splice(index, 1, newValue)`<br><br>在<b>Vue 3</b>中，由于使用了`Proxy`，上述第二种情况的限制<b>已不复存在</b>，可以直接通过索引修改并触发更新。"	Vue 响应式原理
VUE_VS_REACT_DIFFERENCES_NEXUS	AnkiNexus	前端::7-框架与生态::架构与设计	Vue和React有哪些主要区别？	"<ul><li>模板语法</li><li>响应式原理与状态管理</li><li>组件通信</li><li>性能优化</li></ul>"	"[{""title"": ""Vue的模板语法与React的JSX有何不同？"", ""deck"": ""前端::7-框架与生态::架构与设计"", ""note_guid"": ""VUE_VS_REACT_TEMPLATE""}, {""title"": ""Vue和React在响应式原理和状态管理上有何不同？"", ""deck"": ""前端::7-框架与生态::架构与设计"", ""note_guid"": ""VUE_VS_REACT_REACTIVITY""}, {""title"": ""Vue和React的组件通信方式有何不同？"", ""deck"": ""前端::7-框架与生态::架构与设计"", ""note_guid"": ""VUE_VS_REACT_COMMUNICATION""}]"	Vue React
VUE_VS_REACT_TEMPLATE	基础	前端::7-框架与生态::架构与设计	Vue的模板语法与React的JSX有何不同？	"<ul><li><b>Vue</b>: 使用基于HTML的<b>模板语法</b>。它更接近传统Web开发，将HTML、CSS和JS在`.vue`文件中分离（但逻辑上耦合）。Vue的模板需要被编译器转换成渲染函数。这使得模板更直观，对设计师更友好。</li><li><b>React</b>: 使用<b>JSX</b> (JavaScript XML)，它允许在JavaScript中编写类似HTML的结构。JSX的哲学是'All in JS'，将结构、逻辑和样式（通过CSS-in-JS）都放在JavaScript中处理。JSX提供了JavaScript的全部编程能力，更灵活。</li></ul>"	Vue React
VUE_VS_REACT_REACTIVITY	基础	前端::7-框架与生态::架构与设计	Vue和React在响应式原理和状态管理上有何不同？	"<ul><li><b>响应式原理</b>：<br>  - <b>Vue</b>: 采用<b>可变数据</b>和<b>自动响应</b>的系统。当修改`data`中的数据时，视图会自动更新。这是通过`Object.defineProperty` (Vue 2) 或 `Proxy` (Vue 3) 实现的。<br>  - <b>React</b>: 强调<b>不可变性</b>。状态是只读的，必须通过`setState` (Class Components) 或 `useState` (Functional Components) 返回的新状态来触发更新。React会通过比较新旧状态来决定是否重新渲染。</li><li><b>状态管理</b>：<br>  - <b>Vue</b>: 官方提供了紧密集成的<b>Vuex</b>（现在是<b>Pinia</b>），提供了一个集中式的store。<br>  - <b>React</b>: 生态系统中有多种选择，如<b>Redux</b>、<b>MobX</b>，以及React内置的<b>Context API</b>。</li></ul>"	Vue React 响应式原理 状态管理
VUE_VS_REACT_COMMUNICATION	基础	前端::7-框架与生态::架构与设计	Vue和React的组件通信方式有何不同？	"虽然两者都支持Props进行父子通信，但在其他方面有差异：<br><ul><li><b>父->子</b>: 都是通过<b>Props</b>。</li><li><b>子->父</b>: <br>  - <b>Vue</b>: 通过`$emit`触发事件，父组件用`v-on`监听。更符合原生DOM事件模型。<br>  - <b>React</b>: 通过父组件向子组件传递一个<b>回调函数</b>作为prop。</li><li><b>跨级/兄弟</b>: <br>  - <b>Vue</b>: 可以使用`provide/inject`或集中的事件总线(Event Bus)，但更推荐使用<b>Vuex/Pinia</b>。<br>  - <b>React</b>: 可以使用<b>Context API</b>进行跨级传递，或使用<b>Redux/MobX</b>等状态管理库。</li></ul>"	Vue React 组件通信
VUE_PARENT_CHILD_PROPS	基础	前端::7-框架与生态::Vue::组件化	在Vue中，父子组件传值最常用和推荐的方式是什么？	最常用和推荐的方式是使用 <b>Props</b> 进行父传子，使用 <b>Events (`$emit`)</b> 进行子传父。<br><br><ul><li><b>父传子 (Props)</b>: 父组件通过在子组件标签上绑定属性（使用`v-bind`或`:`)来传递数据，子组件通过`props`选项来接收。这是单向数据流，子组件不应直接修改prop。</li><li><b>子传父 (Events)</b>: 子组件通过调用`this.$emit('custom-event-name', data)`来触发一个自定义事件，并可以附带数据。父组件通过在子组件标签上使用`v-on`或`@`来监听这个事件并执行相应的方法。</li></ul>这种方式使得数据流向清晰、可追溯，是构建健壮组件的最佳实践。	Vue 组件通信 Props
