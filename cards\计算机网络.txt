#separator:tab
#html:true
#guid column:1
#notetype column:2
#deck column:3
#tags column:7
NET_HTTP_NEXUS	AnkiNexus	前端::0-计算机科学基础::计算机网络::HTTP协议	关于HTTP协议，应掌握哪些核心要点？	"<ul><li>HTTP方法与语义（GET/POST/PUT/DELETE/HEAD/OPTIONS/CONNECT/TRACE）</li><li>请求报文与响应报文结构</li><li>常见请求/响应头与Content-Type</li><li>HTTP/1.0 vs 1.1、HTTP/2的关键差异</li><li>HTTP与HTTPS的区别、Keep-Alive</li><li>URL的组成、缓存相关请求头、304语义</li><li>输入URL到页面呈现的全过程（含DNS、TCP、TLS）</li><li>HTTP/3与QUIC概览</li></ul>"	"[{""title"": ""HTTP中GET与POST的区别是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_GET_VS_POST""}, {""title"": ""POST与PUT的语义有何不同？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_POST_VS_PUT""}, {""title"": ""列举常见的HTTP请求头与响应头（以及常见Content-Type）"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_COMMON_HEADERS""}, {""title"": ""HTTP/1.0 与 HTTP/1.1 的核心差异是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_10_VS_11""}, {""title"": ""HTTP/1.1 与 HTTP/2 的核心差异是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_11_VS_2""}, {""title"": ""HTTP 与 HTTPS 的区别是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_HTTP_VS_HTTPS""}, {""title"": ""当在浏览器地址栏输入网址并回车后，网络层面发生了什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_URL_TO_RENDER""}, {""title"": ""什么是HTTP的Keep-Alive（长连接）？握手与优缺点？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_KEEP_ALIVE""}, {""title"": ""OPTIONS方法的用途与典型使用场景是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_OPTIONS_USAGE""}, {""title"": ""URL由哪些部分组成？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_URL_PARTS""}, {""title"": ""与缓存相关的常见请求/响应头有哪些？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_CACHE_HEADERS""}, {""title"": ""如何理解304状态码及其出现过多的影响？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP_304_MEANING""}, {""title"": ""HTTP/3(QUIC) 的关键特性概览"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP协议"", ""note_guid"": ""NET_HTTP3_QUIC""}]"	HTTP
NET_HTTP_GET_VS_POST	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	HTTP中GET与POST的区别是什么？	"<ul><li>幂等性：GET幂等，POST非幂等（常用于创建/变更）</li><li>缓存：浏览器通常会缓存GET，较少缓存POST</li><li>报文：GET实体通常为空；POST实体承载提交数据</li><li>安全性：GET参数出现在URL历史中，较不安全；POST较安全</li><li>长度：GET受URL长度限制（浏览器/服务器实现限制）</li><li>参数类型：POST支持更丰富的数据类型</li></ul>"	HTTP GET POST
NET_HTTP_POST_VS_PUT	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	POST与PUT的语义有何不同？	"<b>PUT</b>：幂等，更新资源的内容，不增加资源的种类；<br><b>POST</b>：非幂等，创建新资源或触发服务器侧处理，常导致资源集合发生变化。"	HTTP PUT POST 语义
NET_HTTP_COMMON_HEADERS	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	列举常见的HTTP请求头与响应头（以及常见Content-Type）	"<b>请求头</b>：Accept, Accept-Charset, Accept-Encoding, Accept-Language, Connection, Cookie, Host, Referer, User-Agent<br><b>响应头</b>：Date, Server, Connection, Cache-Control, Content-Type<br><b>常见Content-Type</b>：application/x-www-form-urlencoded, multipart/form-data, application/json, text/xml"	HTTP Header Content-Type
NET_HTTP_10_VS_11	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	HTTP/1.0 与 HTTP/1.1 的核心差异是什么？	"<ul><li>连接：1.0默认短连接；1.1默认持久连接（Keep-Alive）</li><li>范围请求：1.1支持Range与206局部内容</li><li>缓存策略：1.1引入Etag/If-None-Match等更丰富控制</li><li>Host头：1.1新增Host以支持虚拟主机</li><li>方法集：1.1增加PUT、HEAD、OPTIONS等</li></ul>"	HTTP 1.0 1.1 对比
NET_HTTP_11_VS_2	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	HTTP/1.1 与 HTTP/2 的核心差异是什么？	"<ul><li>二进制分帧：头与体均为二进制帧</li><li>多路复用：单连接并行多流，缓解队头阻塞</li><li>数据流标识：每个请求是独立Stream</li><li>头部压缩：HPACK静态/动态表+哈夫曼编码</li><li>服务器推送：服务端可主动推送静态资源</li></ul>"	HTTP2 多路复用 头部压缩
NET_HTTP_HTTP_VS_HTTPS	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	HTTP 与 HTTPS 的区别是什么？	"<ul><li>是否加密：HTTP明文；HTTPS基于TLS加密</li><li>认证与完整性：HTTPS提供身份认证与完整性校验</li><li>端口：HTTP 80；HTTPS 443</li><li>成本与握手：HTTPS需证书，握手更耗时/复杂</li></ul>"	HTTP HTTPS 区别
NET_HTTP_URL_TO_RENDER	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	当在浏览器地址栏输入网址并回车后，网络层面发生了什么？	"<ol><li>解析URL与缓存判断</li><li>DNS解析（本地/递归+迭代）</li><li>ARP获取MAC（同网段或网关）</li><li>TCP三次握手</li><li>（HTTPS）TLS握手</li><li>发送HTTP请求并接收响应</li><li>渲染流水线（构建DOM/CSSOM→合成→绘制）</li><li>TCP四次挥手</li></ol>"	HTTP 浏览器流程 DNS TCP TLS 渲染
NET_HTTP_KEEP_ALIVE	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	什么是HTTP的Keep-Alive（长连接）？握手与优缺点？	"<b>定义</b>：复用TCP连接以承载多次请求/响应。<br><b>建立</b>：1.0需显式'Connection: keep-alive'；1.1默认长连；关闭用'Connection: close'。<br><b>优点</b>：降低握手开销、减少拥塞、允许管线化、降低延迟。<br><b>缺点</b>：长时间占用可能导致资源浪费。"	HTTP Keep-Alive 长连接
NET_HTTP_OPTIONS_USAGE	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	OPTIONS方法的用途与典型使用场景是什么？	"<ul><li>探测资源所支持的方法（Allow/Access-Control-Allow-Methods）</li><li>复杂CORS预检请求，检查权限与协商跨域参数</li></ul>"	HTTP OPTIONS CORS 预检
NET_HTTP_URL_PARTS	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	URL由哪些部分组成？	"协议、域名、端口、虚拟目录、文件名、查询参数、锚（fragment）。例如：protocol://host:port/path/file?query#hash"	HTTP URL 组成
NET_HTTP_CACHE_HEADERS	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	与缓存相关的常见请求/响应头有哪些？	"<b>强缓存</b>：Expires，Cache-Control（max-age等）<br><b>协商缓存</b>：ETag/If-None-Match，Last-Modified/If-Modified-Since"	HTTP 缓存 ETag Cache-Control
NET_HTTP_304_MEANING	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	如何理解304状态码及其出现过多的影响？	"304表示'命中协商缓存'，告知客户端使用本地副本；非错误。大量304可能影响搜索引擎抓取频率（站点快照、收录、权重）。"	HTTP 304 缓存 SEO
NET_HTTP3_QUIC	基础	前端::0-计算机科学基础::计算机网络::HTTP协议	HTTP/3(QUIC) 的关键特性概览	"基于UDP的QUIC提供可靠传输、拥塞控制、0-RTT/1-RTT握手、多路复用无队头阻塞，并集成TLS1.3。"	HTTP3 QUIC 概览
NET_HTTPS_NEXUS	AnkiNexus	前端::0-计算机科学基础::计算机网络::HTTPS协议	关于HTTPS/TLS，应掌握哪些核心要点？	"<ul><li>HTTPS定义与动机：身份认证、加密、完整性</li><li>TLS三类基础算法：散列函数、对称加密、非对称加密</li><li>数字证书与CA信任链</li><li>握手流程（客户端/服务器随机数与密钥协商）</li><li>优缺点与性能考量</li><li>如何综合使用非对称+对称加密抵御中间人</li></ul>"	"[{""title"": ""什么是HTTPS？它解决了哪些安全问题？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTPS协议"", ""note_guid"": ""NET_HTTPS_WHAT""}, {""title"": ""TLS的三类基础算法及其作用分别是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTPS协议"", ""note_guid"": ""NET_TLS_PRIMITIVES""}, {""title"": ""数字证书是什么？如何防中间人伪造公钥？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTPS协议"", ""note_guid"": ""NET_CERT_WHAT""}, {""title"": ""HTTPS握手的大致流程是怎样的？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTPS协议"", ""note_guid"": ""NET_HTTPS_HANDSHAKE""}, {""title"": ""HTTPS的优缺点分别有哪些？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTPS协议"", ""note_guid"": ""NET_HTTPS_PROS_CONS""}, {""title"": ""HTTPS如何综合使用非对称与对称加密以保证安全？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTPS协议"", ""note_guid"": ""NET_HTTPS_HOW_SECURE""}]"	HTTPS TLS 证书
NET_HTTPS_WHAT	基础	前端::0-计算机科学基础::计算机网络::HTTPS协议	什么是HTTPS？它解决了哪些安全问题？	"HTTPS=HTTP+TLS/SSL，通过加密、认证、完整性校验抵御窃听、篡改与劫持。"	HTTPS 定义 安全
NET_TLS_PRIMITIVES	基础	前端::0-计算机科学基础::计算机网络::HTTPS协议	TLS的三类基础算法及其作用分别是什么？	"<ul><li>散列函数：完整性校验</li><li>对称加密：会话期数据机密性</li><li>非对称加密：身份认证与密钥协商</li></ul>"	TLS 算法 散列 对称 非对称
NET_CERT_WHAT	基础	前端::0-计算机科学基础::计算机网络::HTTPS协议	数字证书是什么？如何防中间人伪造公钥？	"CA用其私钥对'公钥+主体信息'的摘要进行签名；浏览器用CA公钥验签，确认证书未被篡改且主体可信，从而防止中间人替换公钥。"	HTTPS 证书 CA 中间人
NET_HTTPS_HANDSHAKE	基础	前端::0-计算机科学基础::计算机网络::HTTPS协议	HTTPS握手的大致流程是怎样的？	"客户端发送版本/随机数/密码套件→服务器返回证书/随机数/套件→客户端验证证书并用公钥加密预主秘钥→双方基于三随机数派生会话秘钥→改用对称加密传输。"	HTTPS 握手 TLS1.2/1.3
NET_HTTPS_PROS_CONS	基础	前端::0-计算机科学基础::计算机网络::HTTPS协议	HTTPS的优缺点分别有哪些？	"<b>优点</b>：认证对端、机密性、完整性；安全性业界成熟。<br><b>缺点</b>：握手耗时/算力、证书成本与运维、连接占用更高。"	HTTPS 利弊 性能
NET_HTTPS_HOW_SECURE	基础	前端::0-计算机科学基础::计算机网络::HTTPS协议	HTTPS如何综合使用非对称与对称加密以保证安全？	"用非对称加密安全地协商出对称密钥；后续用对称加密进行高效数据机密性保护，同时配合摘要与MAC/AEAD保障完整性与鉴别。"	HTTPS 加密 设计
NET_STATUS_NEXUS	AnkiNexus	前端::0-计算机科学基础::计算机网络::HTTP状态码	HTTP状态码体系需要掌握哪些？	"<ul><li>五大类：1xx/2xx/3xx/4xx/5xx</li><li>常见2xx：200/204/206</li><li>常见3xx：301/302/303/304/307</li><li>常见4xx：400/401/403/404/405</li><li>常见5xx：500/502/503/504</li><li>307/303/302差异</li></ul>"	"[{""title"": ""HTTP状态码的五大类分别代表什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP状态码"", ""note_guid"": ""NET_STATUS_CATEGORIES""}, {""title"": ""200/204/206分别表示什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP状态码"", ""note_guid"": ""NET_STATUS_2XX""}, {""title"": ""301与302的区别与典型场景？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP状态码"", ""note_guid"": ""NET_STATUS_301_302""}, {""title"": ""303和307分别是什么意思？与302有何差异？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP状态码"", ""note_guid"": ""NET_STATUS_303_307""}, {""title"": ""304 Not Modified的含义与条件请求是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP状态码"", ""note_guid"": ""NET_STATUS_304""}, {""title"": ""400/401/403/404/405分别在什么情况下出现？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP状态码"", ""note_guid"": ""NET_STATUS_4XX""}, {""title"": ""500/502/503/504分别表示什么？常见触发原因？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP状态码"", ""note_guid"": ""NET_STATUS_5XX""}, {""title"": ""同为重定向：307、303、302的区别？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::HTTP状态码"", ""note_guid"": ""NET_STATUS_307_303_302_DIFF""}]"	HTTP 状态码
NET_STATUS_CATEGORIES	基础	前端::0-计算机科学基础::计算机网络::HTTP状态码	HTTP状态码的五大类分别代表什么？	"1xx信息；2xx成功；3xx重定向；4xx客户端错误；5xx服务器错误。"	HTTP 状态码 概览
NET_STATUS_2XX	基础	前端::0-计算机科学基础::计算机网络::HTTP状态码	200/204/206分别表示什么？	"200：请求成功；204：成功但无实体；206：范围请求的部分内容。"	HTTP 2xx
NET_STATUS_301_302	基础	前端::0-计算机科学基础::计算机网络::HTTP状态码	301与302的区别与典型场景？	"301永久重定向（更新书签与搜索引擎收录）；302临时重定向（活动页、未登录跳转等）。"	HTTP 3xx 301 302
NET_STATUS_303_307	基础	前端::0-计算机科学基础::计算机网络::HTTP状态码	303和307分别是什么意思？与302有何差异？	"303明确要求用GET获取另一个URI；307临时重定向且保持方法不变；302历史上常被当作GET重定向处理。"	HTTP 303 307 302
NET_STATUS_304	基础	前端::0-计算机科学基础::计算机网络::HTTP状态码	304 Not Modified的含义与条件请求是什么？	"命中协商缓存；条件请求头如If-None-Match/If-Modified-Since控制。"	HTTP 304 缓存
NET_STATUS_4XX	基础	前端::0-计算机科学基础::计算机网络::HTTP状态码	400/401/403/404/405分别在什么情况下出现？	"400：语法错误；401：未通过认证（含WWW-Authenticate质询）；403：禁止访问；404：资源不存在；405：方法不被允许。"	HTTP 4xx
NET_STATUS_5XX	基础	前端::0-计算机科学基础::计算机网络::HTTP状态码	500/502/503/504分别表示什么？常见触发原因？	"500：服务器错误；502：网关上游无效响应；503：过载/维护；504：网关超时（执行超时或死循环等）。"	HTTP 5xx
NET_STATUS_307_303_302_DIFF	基础	前端::0-计算机科学基础::计算机网络::HTTP状态码	同为重定向：307、303、302的区别？	"HTTP/1.1细化302：303改为GET；307要求保持原方法，遵循规范不变更POST为GET。"	HTTP 重定向 差异
NET_DNS_NEXUS	AnkiNexus	前端::0-计算机科学基础::计算机网络::DNS协议	关于DNS，需要掌握哪些要点？	"<ul><li>DNS的概念与作用</li><li>端口与协议：53端口，解析用UDP，区域传送用TCP</li><li>完整解析流程（缓存→本地DNS→根→顶级→权威）</li><li>递归与迭代查询的区别</li><li>常见记录类型A/NS/CNAME/MX</li></ul>"	"[{""title"": ""什么是DNS？它解决了什么问题？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::DNS协议"", ""note_guid"": ""NET_DNS_WHAT""}, {""title"": ""DNS为何同时用TCP与UDP？各自使用在哪些场景？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::DNS协议"", ""note_guid"": ""NET_DNS_TCP_UDP""}, {""title"": ""DNS完整解析流程是怎样的？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::DNS协议"", ""note_guid"": ""NET_DNS_RESOLUTION""}, {""title"": ""递归查询与迭代查询有何区别？本地DNS分别如何使用？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::DNS协议"", ""note_guid"": ""NET_DNS_RECURSIVE_ITERATIVE""}, {""title"": ""常见的DNS记录类型及其含义？（A/NS/CNAME/MX）"", ""deck"": ""前端::0-计算机科学基础::计算机网络::DNS协议"", ""note_guid"": ""NET_DNS_RECORD_TYPES""}]"	DNS 解析 记录
NET_DNS_WHAT	基础	前端::0-计算机科学基础::计算机网络::DNS协议	什么是DNS？它解决了什么问题？	"DNS是分布式的域名系统，将易记的域名解析为IP地址，便于访问互联网资源。"	DNS 定义 作用
NET_DNS_TCP_UDP	基础	前端::0-计算机科学基础::计算机网络::DNS协议	DNS为何同时用TCP与UDP？各自使用在哪些场景？	"解析查询多用UDP（小于约512B、低开销）；区域传送用TCP（可靠、数据量大）。"	DNS TCP UDP 53端口
NET_DNS_RESOLUTION	基础	前端::0-计算机科学基础::计算机网络::DNS协议	DNS完整解析流程是怎样的？	"浏览器缓存→本地DNS缓存→根→顶级→权威，逐级获取下一跳直至得到最终记录，结果回填缓存。"	DNS 解析 流程
NET_DNS_RECURSIVE_ITERATIVE	基础	前端::0-计算机科学基础::计算机网络::DNS协议	递归查询与迭代查询有何区别？本地DNS分别如何使用？	"递归：代用户完成全流程并返回最终结果；迭代：每级仅返回下一步线索。用户对本地DNS是递归；本地DNS对上级通常是迭代。"	DNS 递归 迭代
NET_DNS_RECORD_TYPES	基础	前端::0-计算机科学基础::计算机网络::DNS协议	常见的DNS记录类型及其含义？（A/NS/CNAME/MX）	"A：主机名→IP；NS：域名→负责该域的DNS主机名；CNAME：别名→规范主机名；MX：邮件别名→邮件服务器主机名。"	DNS 记录 类型
NET_MODEL_NEXUS	AnkiNexus	前端::0-计算机科学基础::计算机网络::网络模型	关于网络模型，需要掌握哪些知识点？	"<ul><li>OSI七层各层职责与典型协议/示例</li><li>TCP/IP五层与OSI映射</li><li>设备与层次（交换机/路由器等）</li></ul>"	"[{""title"": ""OSI七层模型各层的核心职责是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::网络模型"", ""note_guid"": ""NET_OSI_LAYERS""}, {""title"": ""TCP/IP五层模型与OSI的对应关系是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::网络模型"", ""note_guid"": ""NET_TCPIP_FIVE""}, {""title"": ""常见网络设备分别工作在第几层？举例说明。"", ""deck"": ""前端::0-计算机科学基础::计算机网络::网络模型"", ""note_guid"": ""NET_DEVICE_LAYER""}]"	网络模型 OSI TCPIP
NET_OSI_LAYERS	基础	前端::0-计算机科学基础::计算机网络::网络模型	OSI七层模型各层的核心职责是什么？	"<ol><li>应用：为应用进程提供服务（HTTP/FTP/SMTP/DNS）</li><li>表示：编解码/加密/压缩（如base64）</li><li>会话：建立/管理/终止会话</li><li>传输：端到端可靠/透明传输（TCP/UDP）</li><li>网络：寻址与路由（IP）</li><li>数据链路：成帧与差错检测（MAC）</li><li>物理：比特传输与电气规范（介质/线缆）</li></ol>"	OSI 职责 分层
NET_TCPIP_FIVE	基础	前端::0-计算机科学基础::计算机网络::网络模型	TCP/IP五层模型与OSI的对应关系是什么？	"TCP/IP将应用/表示/会话合并为应用层；其余为传输、网络、数据链路、物理。应用映射HTTP/FTP/DNS等；传输映射TCP/UDP；网络映射IP；链路映射以太网；物理为介质。"	TCPIP 五层 映射
NET_DEVICE_LAYER	基础	前端::0-计算机科学基础::计算机网络::网络模型	常见网络设备分别工作在第几层？举例说明。	"交换机通常在数据链路层；路由器在网络层；集线器/网线等在物理层。"	设备 分层
NET_TCP_UDP_NEXUS	AnkiNexus	前端::0-计算机科学基础::计算机网络::TCP与UDP	关于TCP与UDP，需要掌握哪些重点？	"<ul><li>UDP与TCP的定义与特性</li><li>两者差异与典型使用场景</li><li>UDP'不可靠'的原因</li><li>TCP：重传、拥塞控制、流量控制、可靠传输</li><li>TCP：三次握手与四次挥手</li><li>TCP粘包与处理；为何UDP无粘包</li></ul>"	"[{""title"": ""UDP的主要特性有哪些？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_UDP_FEATURES""}, {""title"": ""TCP的主要特性有哪些？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_FEATURES""}, {""title"": ""TCP与UDP的核心差异与典型场景？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_UDP_DIFF_USE""}, {""title"": ""UDP为何被认为'不可靠'？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_UDP_UNRELIABLE""}, {""title"": ""TCP的重传机制如何工作？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_RETX""}, {""title"": ""TCP的拥塞控制包含哪些机制？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_CONGESTION""}, {""title"": ""TCP的流量控制如何实现？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_FLOWCTRL""}, {""title"": ""TCP的可靠传输机制（窗口与ARQ）是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_RELIABLE""}, {""title"": ""TCP三次握手的目的与过程是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_3WH""}, {""title"": ""TCP四次挥手的过程与原因是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_4WF""}, {""title"": ""TCP粘包的现象与常见解决方案？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_TCP_STICKY""}, {""title"": ""为什么UDP不会出现'粘包'？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::TCP与UDP"", ""note_guid"": ""NET_UDP_NO_STICKY""}]"	TCP UDP 重传 拥塞 粘包
NET_UDP_FEATURES	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	UDP的主要特性有哪些？	"无连接；支持单播/多播/广播；面向报文；不可靠（不确认/不重传/无拥塞控制）；头部8字节，效率高。"	UDP 特性
NET_TCP_FEATURES	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP的主要特性有哪些？	"面向连接；仅单播；面向字节流；可靠传输（序号/ACK/重传）；拥塞控制；全双工通信。"	TCP 特性
NET_TCP_UDP_DIFF_USE	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP与UDP的核心差异与典型场景？	"差异：连接/可靠性/报文与流/头部开销等。场景：TCP用于文件/邮件/远程登录；UDP用于实时音视频/语音/直播/广播等。"	TCP UDP 场景 比较
NET_UDP_UNRELIABLE	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	UDP为何被认为'不可靠'？	"不建立连接；不确认、不重传；不保证顺序；不跟踪状态；无拥塞控制。"	UDP 不可靠
NET_TCP_RETX	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP的重传机制如何工作？	"超时重传：定时器超时触发；冗余ACK的快速重传：收到3个重复ACK立即重传丢失段。"	TCP 重传 超时 快速重传
NET_TCP_CONGESTION	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP的拥塞控制包含哪些机制？	"慢启动→拥塞避免；快速重传→快速恢复；拥塞时降低阈值并调整拥塞窗口。"	TCP 拥塞控制
NET_TCP_FLOWCTRL	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP的流量控制如何实现？	"基于可变大小的滑动窗口；接收端通过窗口通告告知剩余缓冲；零窗口暂停发送，恢复后继续。"	TCP 流量控制 滑动窗口
NET_TCP_RELIABLE	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP的可靠传输机制（窗口与ARQ）是什么？	"发送窗口管理已确认/未确认/待发送段，定时器/累计确认配合；类似选择重传的混合实现以提升效率。"	TCP 可靠 选择重传 窗口
NET_TCP_3WH	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP三次握手的目的与过程是什么？	"目的：相互确认收发能力与初始序号，防止旧报文影响新连接。<br>过程：SYN→SYN+ACK→ACK，双方进入ESTABLISHED。"	TCP 三次握手
NET_TCP_4WF	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP四次挥手的过程与原因是什么？	"FIN→ACK（半关闭）→FIN→ACK；因全双工需分别关闭两个方向，且客户端TIME-WAIT以防ACK丢失。"	TCP 四次挥手 TIME-WAIT
NET_TCP_STICKY	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	TCP粘包的现象与常见解决方案？	"现象：多次send在接收端粘连或拆分。<br>方案：发送间隔等待；关闭Nagle（有限场景）；应用层封包/拆包（长度前缀/分隔符/定长）。"	TCP 粘包 Nagle 封包
NET_UDP_NO_STICKY	基础	前端::0-计算机科学基础::计算机网络::TCP与UDP	为什么UDP不会出现'粘包'？	"UDP面向消息并保护消息边界；每个数据报独立递交，接收端按报文粒度读取。"	UDP 消息边界
NET_WS_NEXUS	AnkiNexus	前端::0-计算机科学基础::计算机网络::WebSocket	关于WebSocket，需要掌握哪些要点？	"<ul><li>定义与场景：基于TCP的全双工协议，可由服务器主动推送</li><li>核心特性：双向、轻量、无同源限制、与HTTP兼容握手</li><li>基本用法示例</li><li>与短轮询/长轮询/SSE的对比</li></ul>"	"[{""title"": ""什么是WebSocket？它的核心特点是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::WebSocket"", ""note_guid"": ""NET_WS_WHAT""}, {""title"": ""WebSocket的常见特性有哪些？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::WebSocket"", ""note_guid"": ""NET_WS_FEATURES""}, {""title"": ""如何在浏览器中简单使用WebSocket？（示例）"", ""deck"": ""前端::0-计算机科学基础::计算机网络::WebSocket"", ""note_guid"": ""NET_WS_USAGE""}, {""title"": ""短轮询/长轮询/SSE 与 WebSocket 的差异是什么？"", ""deck"": ""前端::0-计算机科学基础::计算机网络::WebSocket"", ""note_guid"": ""NET_WS_VS_POLLING_SSE""}]"	WebSocket 即时通信
NET_WS_WHAT	基础	前端::0-计算机科学基础::计算机网络::WebSocket	什么是WebSocket？它的核心特点是什么？	"基于TCP、复用HTTP握手通道的全双工协议；单次握手建立持久连接，服务端可主动推送，客户端也可主动发送。"	WebSocket 定义 特点
NET_WS_FEATURES	基础	前端::0-计算机科学基础::计算机网络::WebSocket	WebSocket的常见特性有哪些？	"双向通信；文本/二进制；轻量高效；无同源限制；默认端口80/443；与HTTP代理/中间设施兼容性好。"	WebSocket 特性
NET_WS_USAGE	基础	前端::0-计算机科学基础::计算机网络::WebSocket	如何在浏览器中简单使用WebSocket？（示例）	"<pre><code>const ws = new WebSocket('ws://localhost:9999');\nws.onopen = () => { ws.send('hello'); };\nws.onmessage = e => console.log(e.data);\nws.onclose = () => console.log('closed');</code></pre>"	WebSocket 用法 示例
NET_WS_VS_POLLING_SSE	基础	前端::0-计算机科学基础::计算机网络::WebSocket	短轮询/长轮询/SSE 与 WebSocket 的差异是什么？	"性能与实时性：WebSocket > SSE(长连接) > 长轮询 > 短轮询；兼容性：相反方向递减。选择需结合场景权衡。"	WebSocket 轮询 SSE 对比

