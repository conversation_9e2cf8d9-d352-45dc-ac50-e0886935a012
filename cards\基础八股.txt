#separator:tab
#html:true
#guid column:1
#notetype column:2
#deck column:3
#tags column:7
JS_INSTANCEOF_NEXUS	AnkiNexus	前端::3-JavaScript::1-语法与数据结构::instanceof	关于 `instanceof` 操作符，需要理解哪几个核心要点？	"<ul><li>核心定义</li><li>工作原理</li><li>缺点</li><li>手动实现</li><li>使用限制</li></ul>"	"[{""title"": ""JavaScript中 `instanceof` 操作符的核心定义是什么？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::instanceof"", ""note_guid"": ""JS_INSTANCEOF_DEF""}, {""title"": ""`instanceof` 操作符的内部工作原理是怎样的？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::instanceof"", ""note_guid"": ""JS_INSTANCEOF_PRINCIPLE""}, {""title"": ""`instanceof` 操作符存在什么主要缺点？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::instanceof"", ""note_guid"": ""JS_INSTANCEOF_DISADVANTAGE""}, {""title"": ""如何手动实现一个功能类似于 `instanceof` 的 `myInstanceOf` 函数？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::instanceof"", ""note_guid"": ""JS_INSTANCEOF_IMPL""}, {""title"": ""`instanceof` 在使用上有什么需要注意的限制？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::instanceof"", ""note_guid"": ""JS_INSTANCEOF_LIMITATIONS""}]"	JavaScript instanceof
JS_INSTANCEOF_DEF	基础	前端::3-JavaScript::1-语法与数据结构::instanceof	JavaScript中 `instanceof` 操作符的核心定义是什么？	`instanceof` 运算符用于检测一个构造函数（constructor）的 `prototype` 属性是否出现在一个实例对象的原型链（prototype chain）上。	JavaScript instanceof
JS_INSTANCEOF_PRINCIPLE	基础	前端::3-JavaScript::1-语法与数据结构::instanceof	`instanceof` 操作符的内部工作原理是怎样的？	`instanceof` 的原理是通过检查目标对象（object）的原型链，看是否能找到构造函数（constructor）的 `prototype` 对象。如果能找到，则返回 `true`；如果遍历完整个原型链都找不到，则返回 `false`。	JavaScript instanceof
JS_INSTANCEOF_DISADVANTAGE	基础	前端::3-JavaScript::1-语法与数据结构::instanceof	`instanceof` 操作符存在什么主要缺点？	由于 `instanceof` 是基于原型链进行检查，如果一个对象的原型链非常深，那么进行检查的效率会相对较低。	JavaScript instanceof
JS_INSTANCEOF_IMPL	基础	前端::3-JavaScript::1-语法与数据结构::instanceof	如何手动实现一个功能类似于 `instanceof` 的 `myInstanceOf` 函数？	"可以遍历对象的原型链，将其与构造函数的 `prototype`进行比较。<pre><code>function myInstanceOf(obj, constructor) {
  // 获取对象的原型
  let proto = Object.getPrototypeOf(obj);
  
  // 遍历原型链
  while (proto) {
    // 如果找到匹配的原型
    if (proto === constructor.prototype) {
      return true;
    }
    // 继续向上查找
    proto = Object.getPrototypeOf(proto);
  }
  
  return false;
}</code></pre>"	JavaScript instanceof
JS_INSTANCEOF_LIMITATIONS	基础	前端::3-JavaScript::1-语法与数据结构::instanceof	`instanceof` 在使用上有什么需要注意的限制？	`instanceof` 只能用于检查对象类型，不能用于检查原始数据类型（如字符串、数字、布尔值等）。对原始类型使用 `instanceof` 会直接返回 `false`。	JavaScript instanceof
JS_NEW_NEXUS	AnkiNexus	前端::3-JavaScript::1-语法与数据结构::new	`new` 操作符在创建一个实例时，具体执行了哪几个步骤？	"<ul><li>创建一个新的空对象。</li><li>将新对象的原型链接到构造函数的 `prototype` 属性。</li><li>将构造函数内部的 `this` 绑定到新创建的对象上并执行构造函数代码。</li><li>根据构造函数的返回值类型决定最终返回的结果。</li></ul>"	"[{""title"": ""`new` 操作符执行的第一步是什么？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::new"", ""note_guid"": ""JS_NEW_STEP1""}, {""title"": ""`new` 操作符如何处理新对象的原型？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::new"", ""note_guid"": ""JS_NEW_STEP2""}, {""title"": ""在 `new` 的执行过程中，构造函数的 `this` 指向哪里？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::new"", ""note_guid"": ""JS_NEW_STEP3""}, {""title"": ""`new` 操作符的最终返回值是如何决定的？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::new"", ""note_guid"": ""JS_NEW_STEP4""}, {""title"": ""如何手写一个 `myNew` 函数来模拟 `new` 操作符的行为？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::new"", ""note_guid"": ""JS_NEW_IMPL""}]"	JavaScript new
JS_NEW_STEP1	基础	前端::3-JavaScript::1-语法与数据结构::new	`new` 操作符执行的第一步是什么？	第一步是创建一个全新的、空的JavaScript对象。	JavaScript new
JS_NEW_STEP2	基础	前端::3-JavaScript::1-语法与数据结构::new	`new` 操作符如何处理新对象的原型？	新创建的空对象的 `[[Prototype]]`（即 `__proto__`）被设置为构造函数的 `prototype` 对象。这步完成了原型链的链接。	JavaScript new
JS_NEW_STEP3	基础	前端::3-JavaScript::1-语法与数据结构::new	在 `new` 的执行过程中，构造函数的 `this` 指向哪里？	构造函数内部的 `this` 上下文被绑定到新创建的对象上，然后执行构造函数内部的代码，为新对象添加属性和方法。	JavaScript new
JS_NEW_STEP4	基础	前端::3-JavaScript::1-语法与数据结构::new	`new` 操作符的最终返回值是如何决定的？	"1.  如果构造函数<b>没有</b>显式 `return`，或者 `return` 一个原始类型值，那么 `new` 操作符会自动返回新创建的对象。
2.  如果构造函数显式 `return` 了一个<b>对象</b>（或函数），那么 `new` 操作符将返回这个被 `return` 的对象，而不是新创建的对象。"	JavaScript new
JS_NEW_IMPL	基础	前端::3-JavaScript::1-语法与数据结构::new	如何手写一个 `myNew` 函数来模拟 `new` 操作符的行为？	"<pre><code>function myNew(Constructor, ...args) {
  // 1. 创建一个新对象，并链接到构造函数的原型
  const obj = Object.create(Constructor.prototype);
  
  // 2. 将 this 指向新对象，并执行构造函数
  const result = Constructor.apply(obj, args);
  
  // 3. 判断构造函数的返回值
  // 如果返回的是对象或函数，则返回该结果
  if (result !== null && (typeof result === 'object' || typeof result === 'function')) {
    return result;
  }
  
  // 否则，返回新创建的对象
  return obj;
}</code></pre>"	JavaScript new
JS_SCOPE_NEXUS	AnkiNexus	前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域	关于JavaScript中的作用域，可以从哪几个方面来理解？	"<ul><li>作用域的核心定义</li><li>作用域的类型</li><li>ES6引入的块级作用域</li><li>变量在作用域中的查找规则</li></ul>"	"[{""title"": ""JavaScript中“作用域” (Scope) 的核心定义是什么？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域"", ""note_guid"": ""JS_SCOPE_DEF""}, {""title"": ""在ES6之前，JavaScript主要存在哪两种类型的作用域？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域"", ""note_guid"": ""JS_SCOPE_TYPES""}, {""title"": ""ES6是如何引入块级作用域的？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域"", ""note_guid"": ""JS_SCOPE_BLOCK""}, {""title"": ""JavaScript引擎是如何根据作用域链来查找变量的？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域"", ""note_guid"": ""JS_SCOPE_CHAIN""}]"	JavaScript 作用域
JS_SCOPE_DEF	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域	JavaScript中“作用域” (Scope) 的核心定义是什么？	作用域是指在程序中，代码定义变量的区域或范围。它规定了变量的可见性（即可访问性）和生命周期。	JavaScript 作用域
JS_SCOPE_TYPES	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域	在ES6之前，JavaScript主要存在哪两种类型的作用域？	"1.  <b>全局作用域 (Global Scope)</b>: 在代码中任何地方都能被访问的变量所在的作用域。
2.  <b>函数作用域 (Function Scope)</b>: 在函数内部定义的变量，只能在该函数内部被访问。"	JavaScript 作用域
JS_SCOPE_BLOCK	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域	ES6是如何引入块级作用域的？	ES6通过 `let` 和 `const` 关键字引入了块级作用域。由这两个关键字声明的变量，其作用域被限制在定义它们的代码块（通常是 `{}` 包围的区域）内，外部无法访问。	JavaScript 作用域
JS_SCOPE_CHAIN	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域	JavaScript引擎是如何根据作用域链来查找变量的？	当代码需要访问一个变量时，JavaScript引擎会首先在当前作用域中查找。如果找不到，它会逐级向外层（父级）作用域继续查找，直到找到该变量或者到达最外层的全局作用域为止。这个由内向外的查找路径就构成了作用域链。	JavaScript 作用域
JS_EVENTFLOW_NEXUS	AnkiNexus	前端::5-浏览器与WebAPI::BOM与事件::事件流	JavaScript的事件流包含哪三个阶段？	"事件流描述了事件在页面元素中传播的顺序，它包含三个阶段：<ol><li>捕获阶段 (Capture Phase)</li><li>目标阶段 (Target Phase)</li><li>冒泡阶段 (Bubbling Phase)</li></ol>"	"[{""title"": ""在JavaScript事件流中，什么是捕获阶段 (Capture Phase)？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件::事件流"", ""note_guid"": ""JS_EVENTFLOW_CAPTURE""}, {""title"": ""在JavaScript事件流中，什么是目标阶段 (Target Phase)？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件::事件流"", ""note_guid"": ""JS_EVENTFLOW_TARGET""}, {""title"": ""在JavaScript事件流中，什么是冒泡阶段 (Bubbling Phase)？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件::事件流"", ""note_guid"": ""JS_EVENTFLOW_BUBBLING""}, {""title"": ""如何阻止事件在事件流中进一步传播？"", ""deck"": ""前端::5-浏览器与WebAPI::BOM与事件::事件流"", ""note_guid"": ""JS_EVENTFLOW_STOP""}]"	JavaScript 事件流
JS_EVENTFLOW_CAPTURE	基础	前端::5-浏览器与WebAPI::BOM与事件::事件流	在JavaScript事件流中，什么是捕获阶段 (Capture Phase)？	事件从最外层的祖先节点（通常是 `window` 或 `document`）开始，逐级向下传播到事件触发的目标元素的父级节点为止。这个由外向内的过程就是捕获阶段。	JavaScript 事件流
JS_EVENTFLOW_TARGET	基础	前端::5-浏览器与WebAPI::BOM与事件::事件流	在JavaScript事件流中，什么是目标阶段 (Target Phase)？	事件到达了它被触发的原始元素（即事件目标，`event.target`）。浏览器会在这个阶段执行绑定在目标元素上的事件监听器。	JavaScript 事件流
JS_EVENTFLOW_BUBBLING	基础	前端::5-浏览器与WebAPI::BOM与事件::事件流	在JavaScript事件流中，什么是冒泡阶段 (Bubbling Phase)？	事件从事件目标（`event.target`）开始，逐级向上传播，经过其所有祖先节点，直到最外层的 `window` 对象。这个由内向外的过程就是冒泡阶段。这是大多数浏览器事件处理的默认行为。	JavaScript 事件流
JS_EVENTFLOW_STOP	基础	前端::5-浏览器与WebAPI::BOM与事件::事件流	如何阻止事件在事件流中进一步传播？	在事件处理函数中调用 `event.stopPropagation()` 方法。这会阻止事件从当前元素继续向外传播（无论是捕获阶段还是冒泡阶段）。	JavaScript 事件流
JS_EVENTLOOP_NEXUS	AnkiNexus	前端::3-JavaScript::4-异步编程-EventLoop-Promise::EventLoop	JavaScript的事件轮询 (Event Loop) 机制涉及到哪些核心概念？	"<ul><li>调用栈 (Call Stack)</li><li>任务队列 (Task Queue)</li><li>宏任务 (Macro Task)</li><li>微任务 (Micro Task)</li><li>事件轮询过程</li></ul>"	"[{""title"": ""在Event Loop中，宏任务 (Macro Task) 和微任务 (Micro Task) 有什么区别？"", ""deck"": ""前端::3-JavaScript::4-异步编程-EventLoop-Promise::EventLoop"", ""note_guid"": ""JS_EVENTLOOP_TASKTYPES""}, {""title"": ""请描述一次完整的事件轮询 (Event Loop) 的执行顺序。"", ""deck"": ""前端::3-JavaScript::4-异步编程-EventLoop-Promise::EventLoop"", ""note_guid"": ""JS_EVENTLOOP_PROCESS""}, {""title"": ""为什么需要将任务划分为宏任务和微任务？"", ""deck"": ""前端::3-JavaScript::4-异步编程-EventLoop-Promise::EventLoop"", ""note_guid"": ""JS_EVENTLOOP_WHY""}]"	JavaScript EventLoop
JS_EVENTLOOP_TASKTYPES	基础	前端::3-JavaScript::4-异步编程-EventLoop-Promise::EventLoop	在Event Loop中，宏任务 (Macro Task) 和微任务 (Micro Task) 有什么区别？	"<b>宏任务 (Macro Task / Task)</b>:
<ul><li>由浏览器或Node环境发起的独立工作单元。</li><li>例子: `setTimeout`, `setInterval`, I/O操作, UI渲染。</li><li>每次事件循环只执行<b>一个</b>宏任务。</li></ul>
<b>微任务 (Micro Task / Job)</b>:
<ul><li>通常是需要尽快执行的异步任务，在当前宏任务执行结束后立即执行。</li><li>例子: `Promise.then()`, `MutationObserver`, `process.nextTick` (Node.js)。</li><li>当前宏任务执行完后，会<b>清空所有</b>微任务队列中的任务，然后再开始下一个宏任务。</li></ul>"	JavaScript EventLoop
JS_EVENTLOOP_PROCESS	基础	前端::3-JavaScript::4-异步编程-EventLoop-Promise::EventLoop	请描述一次完整的事件轮询 (Event Loop) 的执行顺序。	"1.  从宏任务队列中取出一个任务，放入调用栈中执行。
2.  执行该宏任务中的所有同步代码。
3.  宏任务执行完毕后，检查微任务队列。
4.  执行<b>所有</b>在微任务队列中的任务，直到微任务队列清空。
5.  如有必要，进行UI渲染。
6.  返回步骤1，开始下一次循环。"	JavaScript EventLoop
JS_EVENTLOOP_WHY	基础	前端::3-JavaScript::4-异步编程-EventLoop-Promise::EventLoop	为什么需要将任务划分为宏任务和微任务？	划分宏任务和微任务是为了在处理高优先级任务和低优先级任务之间取得平衡。微任务提供了一种机制，允许我们在当前宏任务执行完毕后、下一次渲染或宏任务开始前，立即执行一些需要高优先处理的异步代码（如Promise回调），这确保了响应的及时性和数据的一致性，避免了不必要的延迟。	JavaScript EventLoop
JS_PROTOTYPECHAIN_NEXUS	AnkiNexus	前端::3-JavaScript::3-原型与继承::原型链	关于JavaScript的原型链，应该理解哪些核心概念？	"<ul><li>原型链的定义</li><li>属性查找机制</li><li>原型链的终点</li><li>原型链的主要作用</li><li>原型链的潜在缺点</li></ul>"	"[{""title"": ""JavaScript中的原型链 (Prototype Chain) 是什么？"", ""deck"": ""前端::3-JavaScript::3-原型与继承::原型链"", ""note_guid"": ""JS_PROTOTYPECHAIN_DEF""}, {""title"": ""当访问一个对象的属性时，原型链是如何进行查找的？"", ""deck"": ""前端::3-JavaScript::3-原型与继承::原型链"", ""note_guid"": ""JS_PROTOTYPECHAIN_LOOKUP""}, {""title"": ""JavaScript原型链的终点是什么？"", ""deck"": ""前端::3-JavaScript::3-原型与继承::原型链"", ""note_guid"": ""JS_PROTOTYPECHAIN_END""}, {""title"": ""原型链在JavaScript中主要起什么作用？"", ""deck"": ""前端::3-JavaScript::3-原型与继承::原型链"", ""note_guid"": ""JS_PROTOTYPECHAIN_PURPOSE""}, {""title"": ""使用原型链会带来哪些潜在的缺点？"", ""deck"": ""前端::3-JavaScript::3-原型与继承::原型链"", ""note_guid"": ""JS_PROTOTYPECHAIN_DISADVANTAGE""}]"	JavaScript 原型链
JS_PROTOTYPECHAIN_DEF	基础	前端::3-JavaScript::3-原型与继承::原型链	JavaScript中的原型链 (Prototype Chain) 是什么？	每个JavaScript对象都有一个私有属性 `[[Prototype]]`（可通过 `__proto__` 或 `Object.getPrototypeOf()` 访问），指向它的构造函数的原型对象（`prototype`）。这个原型对象自己也有一个原型，如此层层向上，直到一个对象的原型为 `null`。这种由 `[[Prototype]]` 链接起来的对象序列就构成了原型链。	JavaScript 原型链
JS_PROTOTYPECHAIN_LOOKUP	基础	前端::3-JavaScript::3-原型与继承::原型链	当访问一个对象的属性时，原型链是如何进行查找的？	当试图访问一个对象的属性时，JavaScript引擎会首先在该对象自身上查找。如果找不到，就会沿着原型链（通过 `[[Prototype]]` 链接）向上查找，访问其原型对象；如果原型对象上还是没有，就继续向上查找，直到找到该属性或到达原型链的末端（`null`）为止。	JavaScript 原型链
JS_PROTOTYPECHAIN_END	基础	前端::3-JavaScript::3-原型与继承::原型链	JavaScript原型链的终点是什么？	原型链的终点是 `null`。`Object.prototype` 对象的 `[[Prototype]]` 就是 `null`。当属性查找到达 `null` 时，如果仍未找到属性，则返回 `undefined`。	JavaScript 原型链
JS_PROTOTYPECHAIN_PURPOSE	基础	前端::3-JavaScript::3-原型与继承::原型链	原型链在JavaScript中主要起什么作用？	原型链是JavaScript实现<b>继承</b>的核心机制。它允许一个对象可以访问并使用定义在其他对象（其原型）上的属性和方法，从而实现代码的复用。	JavaScript 原型链
JS_PROTOTYPECHAIN_DISADVANTAGE	基础	前端::3-JavaScript::3-原型与继承::原型链	使用原型链会带来哪些潜在的缺点？	"1.  <b>性能影响</b>：在原型链上查找属性比访问自有属性要耗时，特别是在原型链很长的情况下。
2.  <b>遍历开销</b>：当试图访问一个不存在的属性时，必须遍历整个原型链，直到终点 `null`，这会带来不必要的性能开销。"	JavaScript 原型链
JS_CLOSURE_NEXUS	AnkiNexus	前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包	关于JavaScript中的闭包 (Closure)，需要掌握哪些核心知识点？	"<ul><li>闭包的定义</li><li>闭包的形成原理</li><li>闭包的优点和主要作用</li><li>闭包的常见使用场景</li><li>使用闭包的注意事项</li><li>闭包是否必然导致内存泄漏？</li></ul>"	"[{""title"": ""JavaScript中的闭包 (Closure) 是什么？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包"", ""note_guid"": ""JS_CLOSURE_DEF""}, {""title"": ""闭包 (Closure) 的形成原理是什么？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包"", ""note_guid"": ""JS_CLOSURE_PRINCIPLE""}, {""title"": ""闭包 (Closure) 的主要作用和优点是什么？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包"", ""note_guid"": ""JS_CLOSURE_ADVANTAGES""}, {""title"": ""列举一些闭包 (Closure) 的常见使用场景。"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包"", ""note_guid"": ""JS_CLOSURE_SCENARIOS""}, {""title"": ""使用闭包时需要注意什么？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包"", ""note_guid"": ""JS_CLOSURE_CAVEATS""}, {""title"": ""闭包一定会造成内存泄漏吗？"", ""deck"": ""前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包"", ""note_guid"": ""JS_CLOSURE_LEAK_Q""}]"	JavaScript 闭包
JS_CLOSURE_DEF	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包	JavaScript中的闭包 (Closure) 是什么？	闭包是指一个函数能够访问并操作其外部（父级）函数作用域中的变量，即使在外部函数执行完毕后。本质上，闭包是由函数以及该函数被声明时所在的作用域环境（Lexical Environment）组合而成的。	JavaScript 闭包
JS_CLOSURE_PRINCIPLE	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包	闭包 (Closure) 的形成原理是什么？	当一个内部函数被从其外部函数中返回或暴露到外部时，它会保留一个对其定义时所在作用域（词法环境）的引用。因此，即使外部函数已经执行完毕，其作用域中的变量也不会被垃圾回收机制销毁，因为内部函数（即闭包）仍然持有对这些变量的引用。	JavaScript 闭包
JS_CLOSURE_ADVANTAGES	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包	闭包 (Closure) 的主要作用和优点是什么？	"1.  <b>数据私有化</b>：创建私有变量，避免全局变量污染。外部只能通过闭包提供的接口访问，无法直接修改。
2.  <b>维持变量状态</b>：让变量的状态得以保持在内存中，不会随着函数的执行完毕而被销毁。"	JavaScript 闭包
JS_CLOSURE_SCENARIOS	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包	列举一些闭包 (Closure) 的常见使用场景。	"<ul><li><b>模块化开发</b>：利用IIFE（立即执行函数表达式）和闭包创建私有作用域，封装模块。</li><li><b>事件处理和回调</b>：在循环中为每个元素绑定事件时，保存正确的索引值。</li><li><b>函数柯里化 (Currying)</b>：创建特定功能的函数。</li><li><b>防抖 (Debounce) 和节流 (Throttle)</b>：使用闭包来保存定时器ID和时间戳。</li><li><b>实现缓存</b>：将计算结果保存在闭包的变量中。</li></ul>"	JavaScript 闭包
JS_CLOSURE_CAVEATS	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包	使用闭包时需要注意什么？	主要需要注意<b>内存管理</b>。因为闭包会使其引用的外部变量一直存在于内存中，如果滥用或者闭包的生命周期过长，可能会导致内存消耗增加，甚至引发内存泄漏。因此，在不需要闭包时，应及时解除对它的引用（例如，将其赋值为 `null`），以便垃圾回收器能够回收其占用的内存。	JavaScript 闭包
JS_CLOSURE_LEAK_Q	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::闭包	闭包一定会造成内存泄漏吗？	不一定。闭包本身是一种正常且强大的语言特性。内存泄漏通常发生在<b>不当使用</b>闭包时，例如，当一个生命周期很长的对象（如DOM元素）持有一个闭包的引用，而这个闭包又引用了其外部作用域的大量数据，并且这个引用关系一直无法被断开时，才会导致内存无法被回收。正确地管理闭包的生命周期可以有效避免内存泄漏。	JavaScript 闭包
JS_BIND_CHAIN	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::bind	如果对一个函数进行连续多次的 `.bind()` 调用，最终函数内的 `this` 会指向哪个对象？	"最终的 `this` 会指向<b>第一个</b>成功绑定的对象。
因为 `Function.prototype.bind()` 的实现原理是，一旦一个函数被绑定了 `this` 上下文，后续的 `bind()` 调用将无法再改变它。后续的绑定操作实际上是作用在 `bind()` 返回的新函数上，但这个新函数的内部 `this` 已经被永久地固定了。"	JavaScript bind
JS_FLOAT_PRECISION	基础	前端::3-JavaScript::1-语法与数据结构::浮点数精度	为什么在JavaScript中 `0.1 + 0.2` 不等于 `0.3`？	"这是因为JavaScript中的数字（Number类型）采用IEEE 754标准的64位双精度浮点数格式进行存储。在这种二进制表示法下，像 `0.1` 和 `0.2` 这样的十进制小数无法被精确地表示，它们会被转换为一个无限循环的二进制小数，并被截断以适应存储精度。
这个微小的精度损失在相加时会累积，导致 `0.1 + 0.2` 的计算结果是一个非常接近 `0.3` 但不完全相等的数字（`0.30000000000000004`）。"	JavaScript 浮点数精度
JS_FLOAT_PRECISION_SOLVE	基础	前端::3-JavaScript::1-语法与数据结构::浮点数精度	如何解决JavaScript中浮点数计算的精度问题？	"常见的解决方案有：
1.  <b>转换为整数计算</b>：将浮点数乘以一个足够大的10的幂（如100），将其变为整数进行计算，然后再除以相同的10的幂。
2.  <b>使用 `toFixed()`</b>：对计算结果使用 `toFixed()` 方法四舍五入到期望的小数位数。<b>注意</b>：`toFixed()` 返回的是字符串，可能需要用 `parseFloat()` 或 `Number()` 转回数字。
3.  <b>使用第三方库</b>：对于需要高精度计算的场景，可以使用 `Decimal.js`、`Big.js` 或 `math.js` 等成熟的库。"	JavaScript 浮点数精度
JS_SYMBOL_NEXUS	AnkiNexus	前端::3-JavaScript::1-语法与数据结构::Symbol	ES6新增的 `Symbol` 基础数据类型有哪些主要用途和特点？	"<ul><li>核心用途：创建唯一标识符</li><li>作为对象属性名以避免冲突</li><li>定义不被常规方法遍历的“隐藏”属性</li><li>用于定义一组常量</li></ul>"	"[{""title"": ""ES6中 `Symbol` 类型的核心用途是什么？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::Symbol"", ""note_guid"": ""JS_SYMBOL_PURPOSE""}, {""title"": ""为什么使用 `Symbol` 作为对象属性名可以避免命名冲突？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::Symbol"", ""note_guid"": ""JS_SYMBOL_PROP_COLLISION""}, {""title"": ""使用 `Symbol` 定义的对象属性在遍历时有什么特点？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::Symbol"", ""note_guid"": ""JS_SYMBOL_ITERATION""}]"	JavaScript Symbol
JS_SYMBOL_PURPOSE	基础	前端::3-JavaScript::1-语法与数据结构::Symbol	ES6中 `Symbol` 类型的核心用途是什么？	`Symbol` 的主要作用是创建一个<b>唯一的、不可变</b>的标识符。每次调用 `Symbol()` 都会返回一个全新的、独一无二的值。	JavaScript Symbol
JS_SYMBOL_PROP_COLLISION	基础	前端::3-JavaScript::1-语法与数据结构::Symbol	为什么使用 `Symbol` 作为对象属性名可以避免命名冲突？	因为每个由 `Symbol()` 生成的值都是独一无二的。即使你创建了多个描述相同的 `Symbol`（例如 `Symbol('foo')` 和 `Symbol('foo')`），它们在内存中也是两个不同的值。这确保了使用 `Symbol` 作为属性名时，不会与对象上任何其他（包括继承的）字符串属性名或 `Symbol` 属性名发生意外冲突，非常适合用于定义内部状态或元数据。	JavaScript Symbol
JS_SYMBOL_ITERATION	基础	前端::3-JavaScript::1-语法与数据结构::Symbol	使用 `Symbol` 定义的对象属性在遍历时有什么特点？	以 `Symbol` 作为键的属性是“隐藏”的，它不会出现在常规的属性遍历操作中，例如 `for...in` 循环、`Object.keys()`、`Object.getOwnPropertyNames()` 或 `JSON.stringify()` 的结果里。要访问 `Symbol` 属性，必须使用 `Object.getOwnPropertySymbols()` 或 `Reflect.ownKeys()`。	JavaScript Symbol
JS_TYPEOF_NULL	基础	前端::3-JavaScript::1-语法与数据结构::typeof	为什么 `typeof null` 的结果是 `"object"`？	这是一个JavaScript语言从最初版本就存在的历史遗留问题。在早期的JavaScript实现中，值的类型标签和值本身存储在一个32位的单元中。对象的类型标签是 `000`。而 `null` 在内部被表示为全零的指针（`NULL` 指针），这恰好与对象类型标签的表示方式吻合。因此，`typeof` 操作符在检查 `null` 时，会错误地将其识别为对象类型。尽管这是一个公认的错误，但为了保持向后兼容，这个行为一直没有被修正。	JavaScript typeof
JS_ES6_FEATURES_NEXUS	AnkiNexus	前端::3-JavaScript::5-ESNext与模块化::ES6	ES6（ECMAScript 2015）引入了哪些重要的新特性？	"ES6引入了大量改进，其中一些核心特性包括：
<ul>
<li>`let` 和 `const` 带来的块级作用域</li>
<li>箭头函数 (`=>`)</li>
<li>模板字符串</li>
<li>解构赋值（数组和对象）</li>
<li>类 (`class`) 和继承 (`extends`)</li>
<li>Promise 对象用于异步编程</li>
<li>模块化 (`import`/`export`)</li>
<li>默认参数、Rest参数、展开运算符</li>
<li>`Symbol` 数据类型</li>
</ul>"	"[{""title"": ""ES6中箭头函数和普通函数有哪些主要区别？"", ""deck"": ""前端::3-JavaScript::5-ESNext与模块化::ES6"", ""note_guid"": ""JS_ES6_ARROW_DIFF""}, {""title"": ""箭头函数可以作为构造函数使用吗？为什么？"", ""deck"": ""前端::3-JavaScript::5-ESNext与模块化::ES6"", ""note_guid"": ""JS_ES6_ARROW_CONSTRUCTOR""}]"	JavaScript ES6
JS_ES6_ARROW_DIFF	基础	前端::3-JavaScript::5-ESNext与模块化::ES6	ES6中箭头函数和普通函数有哪些主要区别？	"1.  <b>`this` 绑定</b>：箭头函数没有自己的 `this`，它会捕获其所在上下文（词法作用域）的 `this` 值。普通函数的 `this` 在调用时确定。
2.  <b>构造函数</b>：箭头函数不能用作构造函数，不能使用 `new` 关键字。
3.  <b>`arguments` 对象</b>：箭头函数没有自己的 `arguments` 对象，但可以访问外部函数的 `arguments`，或者使用 rest 参数 (`...args`)。
4.  <b>`prototype` 属性</b>：箭头函数没有 `prototype` 属性。
5.  <b>语法</b>：箭头函数语法更简洁。"	JavaScript ES6 箭头函数
JS_ES6_ARROW_CONSTRUCTOR	基础	前端::3-JavaScript::5-ESNext与模块化::ES6	箭头函数可以作为构造函数使用吗？为什么？	不可以。箭头函数被设计为非方法函数，它没有内部的 `[[Construct]]` 方法，也没有 `prototype` 属性。因此，对箭头函数使用 `new` 关键字会抛出一个 `TypeError`。	JavaScript ES6 箭头函数
JS_PROMISE_ALL_VS_ALLSETTLED	基础	前端::3-JavaScript::4-异步编程-EventLoop-Promise::Promise	`Promise.all()` 和 `Promise.allSettled()` 的核心区别是什么？	"核心区别在于对失败（rejected）Promise的处理方式：
<ul>
<li><b>`Promise.all()` (短路机制)</b>:
<ul><li>当所有Promise都成功（fulfilled）时，它才会成功，并返回一个包含所有结果的数组。</li><li>只要有一个Promise失败（rejected），`Promise.all()` 就会<b>立即失败</b>，并返回那个失败Promise的原因。</li></ul>
</li>
<li><b>`Promise.allSettled()` (非短路机制)</b>:
<ul><li>它总是会等待<b>所有</b>的Promise都执行完毕（无论是成功还是失败）。</li><li>它永远不会失败，最终会返回一个包含每个Promise结果对象（status, value/reason）的数组，让你能够知道每个Promise的确切状态。</li></ul>
</li>
</ul>
<b>总结</b>：用 `all` 在乎“全成功”，用 `allSettled` 在乎“全有结果”。"	JavaScript Promise
JS_SUBSTRING_VS_SUBSTR	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript中 `substring()` 和 `substr()` 方法有什么区别?	"两者的主要区别在于第二个参数的含义：
<ul>
<li>`substring(startIndex, endIndex)`: 第二个参数是<b>结束位置的索引</b>（不包含该位置的字符）。</li>
<li>`substr(startIndex, length)`: 第二个参数是需要<b>截取的字符数量</b>。</li>
</ul>"	JavaScript 字符串
JS_ASYNC_SCRIPT_NEXUS	AnkiNexus	前端::5-浏览器与WebAPI::浏览器	在JavaScript中，实现脚本异步加载主要有哪几种方式及其优点是什么？	"<ul><li>动态创建 &lt;script&gt; 标签</li><li>使用 XMLHttpRequest 或 Fetch API</li><li>异步加载的优点</li></ul>"	"[{""title"": ""如何通过动态创建<script>标签来实现JavaScript脚本的异步加载？"", ""deck"": ""前端::5-浏览器与WebAPI::浏览器"", ""note_guid"": ""JS_ASYNC_SCRIPT_DYNAMIC_TAG""}, {""title"": ""如何使用XMLHttpRequest或Fetch API来实现JavaScript脚本的异步加载？"", ""deck"": ""前端::5-浏览器与WebAPI::浏览器"", ""note_guid"": ""JS_ASYNC_SCRIPT_XHR_FETCH""}, {""title"": ""与同步加载相比，异步加载JavaScript脚本有哪些主要优点？"", ""deck"": ""前端::5-浏览器与WebAPI::浏览器"", ""note_guid"": ""JS_ASYNC_SCRIPT_BENEFITS""}]"	JavaScript 异步加载
JS_ASYNC_SCRIPT_DYNAMIC_TAG	基础	前端::5-浏览器与WebAPI::浏览器	如何通过动态创建<script>标签来实现JavaScript脚本的异步加载？	"通过 `document.createElement('script')` 创建一个新的script元素，设置其 `src` 属性为脚本的URL，然后将其添加到文档中（例如 `document.body.appendChild(script)`）。可以监听其 `onload` 事件来确保在脚本加载并执行完毕后执行回调函数。"	JavaScript 异步加载
JS_ASYNC_SCRIPT_XHR_FETCH	基础	前端::5-浏览器与WebAPI::浏览器	如何使用XMLHttpRequest或Fetch API来实现JavaScript脚本的异步加载？	"使用 `XMLHttpRequest` 或 `Fetch API` 发送一个GET请求来获取脚本内容。请求成功后，将获取到的响应文本（`responseText`）作为内容，动态创建一个 `<script>` 标签并插入到文档的 `<head>` 中。这种方式可以让你在执行脚本前对其内容进行处理。"	JavaScript 异步加载
JS_ASYNC_SCRIPT_BENEFITS	基础	前端::5-浏览器与WebAPI::浏览器	与同步加载相比，异步加载JavaScript脚本有哪些主要优点？	"<ul><li><b>提升页面性能</b>：不会阻塞浏览器解析HTML和渲染页面，避免页面卡顿。</li><li><b>避免资源阻塞</b>：允许页面的其他资源（如图片、CSS）与脚本并行加载。</li><li><b>灵活控制</b>：可以更灵活地控制脚本的加载时机和执行顺序。</li></ul>"	JavaScript 异步加载
JS_FORIN_VS_FOROF	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript中的 'for...in' 和 'for...of' 循环有什么核心区别？	"<b>for...in</b>:
<ul><li>遍历对象<b>可枚举的属性名称（键名）</b>。</li><li>会遍历对象自有属性和继承自原型链的属性。</li><li>不适合用来遍历数组。</li></ul>
<b>for...of</b>:
<ul><li>遍历<b>可迭代对象（Iterable）的元素值</b>。</li><li>适用于数组、字符串、Set、Map等实现了迭代器接口的对象。</li><li>不能用于遍历普通对象，因为普通对象默认不是可迭代的。</li></ul>"	JavaScript 循环
JS_TYPE_CHECKING_NEXUS	AnkiNexus	前端::3-JavaScript::1-语法与数据结构::typeof	在JavaScript中，判断数据类型常用的方法有哪些？	"<ul><li>`typeof` 操作符</li><li>`instanceof` 操作符</li><li>`Object.prototype.toString.call()`</li></ul>"	"[{""title"": ""如何使用 'typeof' 操作符判断JavaScript数据类型？它有什么限制？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::typeof"", ""note_guid"": ""JS_TYPE_CHECKING_TYPEOF""}, {""title"": ""如何使用 'instanceof' 操作符判断JavaScript数据类型？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::typeof"", ""note_guid"": ""JS_TYPE_CHECKING_INSTANCEOF""}, {""title"": ""为什么 'Object.prototype.toString.call()' 是一个可靠的判断JavaScript数据类型的方法？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构::typeof"", ""note_guid"": ""JS_TYPE_CHECKING_TOSTRING""}]"	JavaScript 数据类型
JS_TYPE_CHECKING_TYPEOF	基础	前端::3-JavaScript::1-语法与数据结构::typeof	如何使用 'typeof' 操作符判断JavaScript数据类型？它有什么限制？	"`typeof` 返回一个表示操作数类型的字符串。它可以准确判断 `undefined`, `boolean`, `number`, `string`, `symbol`, `function`。主要限制是：
<ul><li>对于任何对象（数组、自定义对象等），它都返回 `"object"`。</li><li>一个著名的历史遗留问题是 `typeof null` 会返回 `"object"`。</li></ul>"	JavaScript 数据类型 typeof
JS_TYPE_CHECKING_INSTANCEOF	基础	前端::3-JavaScript::1-语法与数据结构::typeof	如何使用 'instanceof' 操作符判断JavaScript数据类型？	"`instanceof` 用于检测一个对象是否是某个构造函数的实例。它通过检查对象的原型链上是否存在该构造函数的 `prototype` 属性来实现。语法为 `object instanceof Constructor`。"	JavaScript 数据类型 instanceof
JS_TYPE_CHECKING_TOSTRING	基础	前端::3-JavaScript::1-语法与数据结构::typeof	为什么 'Object.prototype.toString.call()' 是一个可靠的判断JavaScript数据类型的方法？	"该方法可以返回一个表示任何值内部 `[[Class]]` 属性的字符串，格式为 `"[object Type]"`。它能够精确地区分各种数据类型，包括基本类型和内置对象，例如 `[object Array]`, `[object Null]`, `[object Object]` 等，弥补了 `typeof` 的不足。"	JavaScript 数据类型
JS_SPLICE_VS_SLICE	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript数组方法 'splice()' 和 'slice()' 的主要区别是什么，尤其是在是否改变原数组方面？	"<b>`splice()`</b>: 会<b>改变</b>原数组。它可以用于在数组中添加、删除或替换元素，并返回被删除的元素组成的数组。

<b>`slice()`</b>: <b>不会</b>改变原数组。它返回一个新的数组，其中包含了原数组中从开始到结束（不含结束）选定的一部分的浅拷贝。"	JavaScript 数组
JS_ARRAY_POP_LAST_NEXUS	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	在JavaScript中，有哪几种常见方法可以删除数组的最后一个元素？	"<ul><li>`pop()`</li><li>`splice()`</li><li>`slice()`</li></ul>"	"[{""title"": ""如何使用 'pop()' 方法删除数组的最后一个元素？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_ARRAY_POP_LAST_POP""}, {""title"": ""如何使用 'splice()' 方法删除数组的最后一个元素？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_ARRAY_POP_LAST_SPLICE""}, {""title"": ""如何使用 'slice()' 方法创建一个不包含最后一个元素的新数组？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_ARRAY_POP_LAST_SLICE""}]"	JavaScript 数组
JS_ARRAY_POP_LAST_POP	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 'pop()' 方法删除数组的最后一个元素？	"调用数组的 `pop()` 方法。该方法会删除数组的最后一个元素，减小数组长度，并返回被删除的元素。这是一个直接修改原数组的操作。
<pre><code>const arr = [1, 2, 3];
arr.pop(); // arr 现在是 [1, 2]
</code></pre>"	JavaScript 数组
JS_ARRAY_POP_LAST_SPLICE	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 'splice()' 方法删除数组的最后一个元素？	"可以调用 `arr.splice(-1, 1)`。这里 `-1` 表示从数组末尾的第一个元素开始，`1` 表示删除一个元素。该方法会修改原数组。
<pre><code>const arr = [1, 2, 3];
arr.splice(-1, 1); // arr 现在是 [1, 2]
</code></pre>"	JavaScript 数组
JS_ARRAY_POP_LAST_SLICE	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 'slice()' 方法创建一个不包含最后一个元素的新数组？	"可以调用 `arr.slice(0, -1)`。这会从索引0开始，截取到倒数第一个元素（不包括）为止，返回一个新数组，原数组不变。
<pre><code>const arr = [1, 2, 3];
const newArr = arr.slice(0, -1); // newArr 是 [1, 2], arr 仍是 [1, 2, 3]
</code></pre>"	JavaScript 数组
JS_EQUALITY_DOUBLE_VS_TRIPLE	基础	前端::3-JavaScript::1-语法与数据结构	JavaScript中的相等运算符 '==' 和 '===' 有什么核心区别？	"<b>`==` (宽松相等)</b>:
<ul><li>在比较前会进行<b>类型转换</b>。例如，`1 == '1'` 会返回 `true`，因为它会将字符串 `'1'` 转换为数字 `1`。</li></ul>
<b>`===` (严格相等)</b>:
<ul><li><b>不进行</b>类型转换。只有当两个值的类型和值都完全相同时，才返回 `true`。例如 `1 === '1'` 会返回 `false`。</li></ul>
<b>最佳实践</b>：为了代码的严谨性和可预测性，应优先使用 `===`。"	JavaScript 相等性
JS_RAF_DEF	基础	前端::5-浏览器与WebAPI::渲染机制-回流-重绘	JavaScript中的 'requestAnimationFrame' 是什么？它的主要用途是什么？	"`requestAnimationFrame` (rAF) 是一个浏览器API，它告诉浏览器你希望执行一个动画，并请求浏览器在下一次重绘（repaint）之前调用一个指定的回调函数来更新动画。它的主要用途是创建平滑、高效的Web动画，因为它与浏览器的渲染周期同步，避免了不必要的渲染和计算，从而减少CPU消耗和页面卡顿。"	JavaScript 动画 API
JS_RIC_DEF	基础	前端::5-浏览器与WebAPI::渲染机制-回流-重绘	JavaScript中的 'requestIdleCallback' 是什么？它的主要用途是什么？	"`requestIdleCallback` 是一个浏览器API，它允许开发者在浏览器主线程处于空闲状态时，执行一些后台或低优先级的任务。它的主要用途是执行那些不紧急、可以延迟的任务（如数据分析、日志上报），而不会阻塞关键的渲染或用户交互，从而提升页面响应性能。"	JavaScript 性能 API
JS_RAF_VS_RIC	基础	前端::5-浏览器与WebAPI::渲染机制-回流-重绘	'requestAnimationFrame' 和 'requestIdleCallback' 的核心应用场景区别是什么？	"<b>`requestAnimationFrame`</b> 专注于<b>渲染相关</b>的任务，它的执行时机与屏幕刷新率紧密相关，理想场景是执行必须在下一次绘制前完成的视觉变化，如动画。

<b>`requestIdleCallback`</b> 专注于<b>非关键性、可延迟</b>的任务，它的执行时机不确定，取决于浏览器是否有空闲时间，理想场景是执行不影响用户界面的后台工作。"	JavaScript API
JS_LET_GLOBAL_WINDOW	基础	前端::3-JavaScript::2-执行上下文-作用域-闭包::作用域	在全局作用域中使用 'let' 声明的变量，是否会成为 'window' 对象的属性？	"不会。与使用 `var` 在全局作用域中声明变量不同，使用 `let`（以及 `const`）声明的全局变量不会被添加到全局对象（在浏览器中即 `window` 对象）上。它们存在于一个独立的、与全局对象并列的“脚本作用域”中。"	JavaScript let var
JS_FOREACH_BREAK	基础	前端::3-JavaScript::1-语法与数据结构	能否在JavaScript数组的 'forEach' 方法中像for循环一样使用 'break' 或 'return' 来提前终止循环？	"不能。在 `forEach` 的回调函数中使用 `return` 仅仅是结束当前这一次迭代，相当于 `for` 循环中的 `continue`，循环会继续执行下一个元素。`forEach` 并没有提供像 `break` 一样直接中断整个循环的机制。如果需要提前终止，应该选择使用传统的 `for` 循环，或者 `for...of`、`some()`、`every()` 等其他数组方法。"	JavaScript forEach
JS_CANVAS_POINT_IN_PATH_NEXUS	AnkiNexus	前端::5-浏览器与WebAPI::Canvas	在HTML5 Canvas中，如何判断一个点是否在某个图形内部？	"<ul><li>对于简单图形，使用 `isPointInPath()`</li><li>对于复杂图形，使用射线法 (Ray Casting Algorithm)</li></ul>"	"[{""title"": ""如何使用Canvas API的 'isPointInPath()' 方法来判断一个点是否在当前路径内？"", ""deck"": ""前端::5-浏览器与WebAPI::Canvas"", ""note_guid"": ""JS_CANVAS_ISPOINTINPATH""}, {""title"": ""对于Canvas中的复杂多边形，射线法 (Ray Casting Algorithm) 判断点是否在图形内的基本原理是什么？"", ""deck"": ""前端::5-浏览器与WebAPI::Canvas"", ""note_guid"": ""JS_CANVAS_RAY_CASTING""}]"	JavaScript Canvas
JS_CANVAS_ISPOINTINPATH	基础	前端::5-浏览器与WebAPI::Canvas	如何使用Canvas API的 'isPointInPath()' 方法来判断一个点是否在当前路径内？	"首先，使用 `beginPath()` 开始并绘制一个路径（如矩形、圆形）。然后，调用 `ctx.isPointInPath(x, y)` 方法，传入要检测的点的坐标。如果该点在当前路径定义的区域内，则返回 `true`，否则返回 `false`。"	JavaScript Canvas
JS_CANVAS_RAY_CASTING	基础	前端::5-浏览器与WebAPI::Canvas	对于Canvas中的复杂多边形，射线法 (Ray Casting Algorithm) 判断点是否在图形内的基本原理是什么？	"从要判断的点向任意方向（通常是水平向右）引出一条射线。然后，计算这条射线与多边形所有边的交点数量。如果交点数量为<b>奇数</b>，则该点在多边形内部；如果为<b>偶数</b>，则在外部。这是处理非自交复杂多边形点包含问题的常用算法。"	JavaScript Canvas
JS_MERGE_OBJECTS_NEXUS	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	在JavaScript中，合并对象的常用方法有哪些？	"<ul><li>`Object.assign()`</li><li>展开运算符 (`...`)</li></ul>"	"[{""title"": ""如何使用 'Object.assign()' 来合并多个JavaScript对象？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_MERGE_OBJECTS_ASSIGN""}, {""title"": ""如何使用展开运算符 '...' 来合并多个JavaScript对象？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_MERGE_OBJECTS_SPREAD""}]"	JavaScript 对象
JS_MERGE_OBJECTS_ASSIGN	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 'Object.assign()' 来合并多个JavaScript对象？	"`Object.assign(target, ...sources)` 方法将所有可枚举的自有属性从一个或多个源对象复制到目标对象，并返回修改后的目标对象。如果存在同名属性，后面的源对象属性会覆盖前面的。
<pre><code>const merged = Object.assign({}, obj1, obj2);</code></pre>"	JavaScript 对象
JS_MERGE_OBJECTS_SPREAD	基础	前端::3-JavaScript::1-语法与数据结构	如何使用展开运算符 '...' 来合并多个JavaScript对象？	"展开运算符 (`...`) 提供了一种更简洁的语法来合并对象。在一个新的对象字面量中，将需要合并的对象依次展开即可。同样，如果存在同名属性，后面的对象属性会覆盖前面的。
<pre><code>const merged = { ...obj1, ...obj2 };</code></pre>"	JavaScript 对象
JS_IS_EMPTY_OBJECT_NEXUS	AnkiNexus	前端::3-JavaScript::1-语法与数据结构	在JavaScript中，有哪几种常用方法可以判断一个对象是否为空对象？	"<ul><li>使用 `Object.keys()`</li><li>使用 `for...in` 循环</li></ul>"	"[{""title"": ""如何使用 'Object.keys()' 来判断一个JavaScript对象是否为空？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_IS_EMPTY_OBJECT_KEYS""}, {""title"": ""如何使用 'for...in' 循环来判断一个JavaScript对象是否为空？"", ""deck"": ""前端::3-JavaScript::1-语法与数据结构"", ""note_guid"": ""JS_IS_EMPTY_OBJECT_FORIN""}]"	JavaScript 对象
JS_IS_EMPTY_OBJECT_KEYS	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 'Object.keys()' 来判断一个JavaScript对象是否为空？	"调用 `Object.keys(obj)` 会返回一个包含该对象所有可枚举的自有属性名组成的数组。通过检查这个数组的 `length` 是否为 `0`，就可以判断该对象是否为空。
<pre><code>Object.keys(obj).length === 0;</code></pre>"	JavaScript 对象
JS_IS_EMPTY_OBJECT_FORIN	基础	前端::3-JavaScript::1-语法与数据结构	如何使用 'for...in' 循环来判断一个JavaScript对象是否为空？	"通过 `for...in` 循环尝试遍历对象的属性。如果循环体能够执行，说明对象至少有一个可枚举的属性，因此不是空对象。可以设置一个标志位，如果循环从未执行，则对象为空。
<pre><code>let isEmpty = true;
for (const prop in obj) {
  isEmpty = false;
  break;
}
// check isEmpty value</code></pre>"	JavaScript 对象
