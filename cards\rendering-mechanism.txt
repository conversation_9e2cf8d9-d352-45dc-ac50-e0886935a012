#separator:tab
#html:true
#guid column:1
#notetype column:2
#deck column:3
#tags column:7
VUE_RENDERING_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::渲染机制	Vue的渲染机制主要包含哪些核心概念？	"<ul><li>虚拟DOM (Virtual DOM)</li><li>渲染管线 (Render Pipeline)</li><li>模板与渲染函数的对比</li><li>带编译时信息的虚拟DOM (Compiler-Informed Virtual DOM)</li></ul>"	"[{""title"" : ""虚拟DOM (Virtual DOM)"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::虚拟DOM"", ""note_guid"" : ""VUE_VDOM_NEXUS""}, {""title"" : ""渲染管线 (Render Pipeline)"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::渲染管线"", ""note_guid"" : ""VUE_PIPELINE_NEXUS""}, {""title"" : ""模板 vs. 渲染函数"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::模板与渲染函数"", ""note_guid"" : ""VUE_TEMPLATE_VS_RENDER_NEXUS""}, {""title"" : ""带编译时信息的虚拟DOM"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_COMPILER_INFORMED_VDOM_NEXUS""}]"	Vue 渲染机制

VUE_VDOM_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::渲染机制::虚拟DOM	虚拟DOM (Virtual DOM) 包含哪些核心知识点？	"<ul><li>定义与核心思想</li><li>vnode</li><li>挂载 (mount)</li><li>更新 (patch)</li><li>主要收益</li></ul>"	"[{""title"" : ""什么是虚拟DOM (Virtual DOM)？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::虚拟DOM"", ""note_guid"" : ""VUE_VDOM_WHAT_IS""}, {""title"" : ""虚拟DOM (VDOM) 的核心思想是什么？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::虚拟DOM"", ""note_guid"" : ""VUE_VDOM_CORE_IDEA""}, {""title"" : ""VDOM中的`vnode`是什么？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::虚拟DOM"", ""note_guid"" : ""VUE_VNODE_WHAT_IS""}, {""title"" : ""VDOM中的“挂载”(mount) 是什么过程？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::虚拟DOM"", ""note_guid"" : ""VUE_VDOM_MOUNT""}, {""title"" : ""VDOM中的“更新”(patch) 是什么过程？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::虚拟DOM"", ""note_guid"" : ""VUE_VDOM_PATCH""}, {""title"" : ""虚拟DOM带来的主要收益是什么？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::虚拟DOM"", ""note_guid"" : ""VUE_VDOM_BENEFITS""}]"	Vue 虚拟DOM

VUE_VDOM_WHAT_IS	基础	前端::7-框架与生态::Vue::渲染机制::虚拟DOM	什么是虚拟DOM (Virtual DOM)？	"虚拟DOM (Virtual DOM, 简称 VDOM) 是一种编程概念，它将UI以数据结构的形式“虚拟”地表示出来，并保存在内存中。它是一种模式而非具体技术，其核心在于通过内存中的虚拟表示来间接操作真实的DOM。"		Vue VDOM 定义
VUE_VDOM_CORE_IDEA	基础	前端::7-框架与生态::Vue::渲染机制::虚拟DOM	虚拟DOM (VDOM) 的核心思想是什么？	"其核心思想是：<br>1. 在内存中用数据结构（如JS对象）描述整个UI状态。<br>2. 当状态变更时，生成一个新的虚拟DOM树。<br>3. 比较新旧两棵树的差异（Diffing）。<br>4. 将差异部分高效地应用到真实的DOM上，最小化DOM操作。"		Vue VDOM 核心思想
VUE_VNODE_WHAT_IS	基础	前端::7-框架与生态::Vue::渲染机制::虚拟DOM	VDOM中的`vnode`是什么？	"`vnode` 是 “virtual node”（虚拟节点）的缩写。它是一个纯粹的JavaScript对象，用来代表一个真实的DOM元素、组件或其他类型的节点。它包含了创建实际元素所需的所有信息，如 `type` (类型), `props` (属性), 和 `children` (子节点)。"		Vue VDOM vnode
VUE_VDOM_MOUNT	基础	前端::7-框架与生态::Vue::渲染机制::虚拟DOM	VDOM中的“挂载”(mount) 是什么过程？	"“挂载” (mount) 是指运行时渲染器遍历一个虚拟DOM (VDOM) 树，并根据其结构递归地创建出真实的DOM节点，然后将这些节点插入到页面中的过程。这是将虚拟表示转换为用户可见UI的初始步骤。"		Vue VDOM 挂载
VUE_VDOM_PATCH	基础	前端::7-框架与生态::Vue::渲染机制::虚拟DOM	VDOM中的“更新”(patch) 是什么过程？	"“更新” (patch)，也称为“比对”(diffing) 或“协调”(reconciliation)，指的是当数据状态发生变化时，渲染器比较新旧两个虚拟DOM树的过程。它会找出两棵树之间的差异，并仅将这些变化高效地应用到真实的DOM上，从而避免了对整个DOM树的完全重绘。"		Vue VDOM 更新 比对
VUE_VDOM_BENEFITS	基础	前端::7-框架与生态::Vue::渲染机制::虚拟DOM	虚拟DOM带来的主要收益是什么？	"主要收益是让开发者能够<b>灵活、声明式地</b>创建、检查和组合UI结构，而将具体、繁琐且易出错的DOM操作完全委托给渲染器处理，从而提升开发效率和代码可维护性。"		Vue VDOM 优点

VUE_PIPELINE_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::渲染机制::渲染管线	Vue的渲染管线包含哪几个阶段？	"1. 编译 (Compile)<br>2. 挂载 (Mount)<br>3. 更新 (Patch)"	"[{""title"" : ""Vue渲染管线中的“编译”阶段是做什么的？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::渲染管线"", ""note_guid"" : ""VUE_PIPELINE_COMPILE""}, {""title"" : ""Vue渲染管线中的“挂载”阶段是做什么的？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::渲染管线"", ""note_guid"" : ""VUE_PIPELINE_MOUNT""}, {""title"" : ""Vue渲染管线中的“更新”阶段是做什么的？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::渲染管线"", ""note_guid"" : ""VUE_PIPELINE_PATCH""}]"	Vue 渲染管线
VUE_PIPELINE_COMPILE	基础	前端::7-框架与生态::Vue::渲染机制::渲染管线	Vue渲染管线中的“编译”阶段是做什么的？	"<b>编译 (Compile)</b> 阶段负责将Vue模板（template）转换为一个<b>渲染函数 (render function)</b>。这个函数执行后会返回一个虚拟DOM树。此步骤可以在构建时预先完成（AOT），也可以在运行时即时完成（JIT）。"		Vue 渲染管线 编译
VUE_PIPELINE_MOUNT	基础	前端::7-框架与生态::Vue::渲染机制::渲染管线	Vue渲染管线中的“挂载”阶段是做什么的？	"<b>挂载 (Mount)</b> 阶段，运行时渲染器会调用编译生成的渲染函数，获取虚拟DOM树，然后遍历该树并创建出实际的DOM节点，最终将其挂载到页面上。此过程会作为响应式副作用执行，因此会追踪所有响应式依赖。"		Vue 渲染管线 挂载
VUE_PIPELINE_PATCH	基础	前端::7-框架与生态::Vue::渲染机制::渲染管线	Vue渲染管线中的“更新”阶段是做什么的？	"<b>更新 (Patch)</b> 阶段，当组件的某个响应式依赖发生变化时，副作用会重新运行，生成一个新的、更新后的虚拟DOM树。渲染器会比较新旧两棵树，找出差异，然后将这些必要的更新应用到真实的DOM上。"		Vue 渲染管线 更新

VUE_TEMPLATE_VS_RENDER_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::渲染机制::模板与渲染函数	对比Vue的模板和渲染函数	"<ul><li>它们之间的关系是什么？</li><li>为什么Vue推荐使用模板？</li><li>渲染函数的适用场景是什么？</li></ul>"	"[{""title"" : ""Vue中模板和渲染函数的关系是什么？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::模板与渲染函数"", ""note_guid"" : ""VUE_TEMPLATE_RELATION""}, {""title"" : ""为什么Vue默认推荐使用模板而不是渲染函数？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::模板与渲染函数"", ""note_guid"" : ""VUE_TEMPLATE_RECOMMENDATION""}, {""title"" : ""在什么场景下渲染函数比模板更适合？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::模板与渲染函数"", ""note_guid"" : ""VUE_RENDER_FUNCTION_USE_CASE""}]"	Vue 模板 渲染函数
VUE_TEMPLATE_RELATION	基础	前端::7-框架与生态::Vue::渲染机制::模板与渲染函数	Vue中模板和渲染函数的关系是什么？	"Vue模板最终会被<b>预编译</b>成渲染函数。渲染函数是更底层的形式，它负责生成虚拟DOM (vnode)。模板提供了一种更声明式、更接近HTML的语法，而渲染函数提供了完全的JavaScript编程能力。"		Vue 模板 渲染函数 关系
VUE_TEMPLATE_RECOMMENDATION	基础	前端::7-框架与生态::Vue::渲染机制::模板与渲染函数	为什么Vue默认推荐使用模板而不是渲染函数？	"主要有两点原因：<br>1. <b>更贴近HTML：</b> 模板易于理解、修改，方便重用HTML片段和应用CSS。<br>2. <b>静态分析优势：</b> 模板的确切语法使得编译器可以进行静态分析，并应用多种编译时优化来提升虚拟DOM的性能，这是手写渲染函数难以做到的。"		Vue 模板 优点
VUE_RENDER_FUNCTION_USE_CASE	基础	前端::7-框架与生态::Vue::渲染机制::模板与渲染函数	在什么场景下渲染函数比模板更适合？	"渲染函数主要用于需要处理<b>高度动态渲染逻辑</b>的可重用组件中。当模板的静态结构不足以表达复杂的逻辑时，可以使用渲染函数，因为它允许你利用JavaScript的全部能力来构建vnode。"		Vue 渲染函数 适用场景

VUE_COMPILER_INFORMED_VDOM_NEXUS	AnkiNexus	前端::7-框架与生态::Vue::渲染机制::编译时优化	什么是“带编译时信息的虚拟DOM”？它包含哪些优化策略？	"它是一种混合解决方案，指Vue通过编译器在编译时静态分析模板，并在生成的代码中留下标记，从而让运行时可以利用这些信息走捷径，避免了纯运行时VDOM的一些暴力更新过程。主要优化策略包括：<br>1. 缓存静态内容<br>2. 更新类型标记<br>3. 树结构打平"	"[{""title"" : ""什么是“带编译时信息的虚拟DOM”？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_COMPILER_INFORMED_VDOM_DEF""}, {""title"" : ""相比纯运行时的VDOM，Vue的“带编译时信息的虚拟DOM”有何优势？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_COMPILER_INFORMED_VDOM_ADVANTAGE""}, {""title"" : ""Vue如何通过“缓存静态内容”优化性能？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_OPTIMIZE_STATIC_CACHE""}, {""title"" : ""Vue中的“静态vnode”是什么？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_OPTIMIZE_STATIC_VNODE""}, {""title"" : ""Vue如何通过“更新类型标记”(Patch Flags)优化性能？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_OPTIMIZE_PATCH_FLAGS""}, {""title"" : ""什么是“更新类型标记”(Patch Flags)？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_PATCH_FLAGS_DEF""}, {""title"" : ""Vue如何通过“树结构打平”(Tree Flattening)优化性能？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_OPTIMIZE_TREE_FLATTENING""}, {""title"" : ""Vue中的“区块”(Block)是什么概念？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_BLOCK_DEF""}, {""title"" : ""“树结构打平”如何影响SSR激活性能？"", ""deck"" : ""前端::7-框架与生态::Vue::渲染机制::编译时优化"", ""note_guid"" : ""VUE_TREE_FLATTENING_SSR""}]"	Vue 编译时优化
VUE_COMPILER_INFORMED_VDOM_DEF	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	什么是“带编译时信息的虚拟DOM”？	"这是一种混合解决方案，指Vue框架同时控制编译器和运行时。编译器在编译时静态分析模板，并在生成的代码中留下优化标记，使得运行时渲染器可以利用这些信息走“捷径”，避免了纯运行时VDOM必须遍历整棵树进行比对的低效过程。"		Vue 编译时优化 VDOM
VUE_COMPILER_INFORMED_VDOM_ADVANTAGE	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	相比纯运行时的VDOM，Vue的“带编译时信息的虚拟DOM”有何优势？	"主要优势是<b>效率</b>。纯运行时VDOM需要暴力遍历和比对整棵树。而Vue的方案通过编译时留下的信息（如静态标记、更新类型标记），让运行时可以：<br>1. 跳过对静态内容的比较。<br>2. 只对动态绑定的部分执行必要的操作。<br>3. 大大减少需要遍历的节点数量。<br>从而在保证声明式写法的同时，获得了更高的性能。"		Vue 编译时优化 VDOM 优势
VUE_OPTIMIZE_STATIC_CACHE	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	Vue如何通过“缓存静态内容”优化性能？	"编译器会识别出模板中完全静态、不带任何动态绑定的部分。在首次渲染时，渲染器会创建这些静态内容的vnode并将其<b>缓存</b>。在后续的重渲染中，会直接复用这些缓存的vnode，完全跳过对它们的创建和差异比对过程。"		Vue 编译时优化 静态内容缓存
VUE_OPTIMIZE_STATIC_VNODE	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	Vue中的“静态vnode”是什么？	"当有足够多连续的静态元素时，Vue编译器会将它们压缩成一个特殊的“静态vnode”。这个vnode包含的是这些节点对应的<b>纯HTML字符串</b>。在挂载时，这些静态节点会直接通过 `innerHTML` 来一次性设置，极大提升了渲染效率。"		Vue 编译时优化 静态vnode
VUE_OPTIMIZE_PATCH_FLAGS	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	Vue如何通过“更新类型标记”(Patch Flags)优化性能？	"编译器在分析模板时，能推断出每个动态绑定元素需要更新的具体类型（如仅class、仅文本等）。它会将这个信息编码为一个数字（即更新类型标记），并附加到vnode上。在更新时，渲染器只需通过快速的<b>位运算</b>检查这个标记，就能知道该执行哪些具体操作，从而避免了不必要的属性比对。"		Vue 编译时优化 更新类型标记
VUE_PATCH_FLAGS_DEF	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	什么是“更新类型标记”(Patch Flags)？	"它是一个由编译器生成的数字，用于标记一个vnode在更新时需要执行的操作类型。例如，`2` 可能代表 `CLASS` 更新，`4` 代表 `TEXT` 更新。一个元素可以有多个标记，它们会被合并成一个数字。运行时通过位运算检查这些标记，以确定需要执行的最少操作。"		Vue 编译时优化 更新类型标记 定义
VUE_OPTIMIZE_TREE_FLATTENING	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	Vue如何通过“树结构打平”(Tree Flattening)优化性能？	"编译器会将一个模板内所有动态的节点提取出来，放进一个打平了的数组中。当组件需要重渲染时，渲染器<b>只需遍历这个打平的数组</b>，而无需遍历整个虚拟DOM树。这大大减少了需要协调的节点数量，静态部分被完全跳过。"		Vue 编译时优化 树结构打平
VUE_BLOCK_DEF	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	Vue中的“区块”(Block)是什么概念？	"在Vue的编译优化中，“区块”(Block) 指的是一个内部结构稳定的节点部分。例如，不包含 `v-if` 或 `v-for` 指令的模板根节点就是一个区块。每个区块会负责追踪其内部所有动态的后代节点（无论层级多深），这是实现“树结构打平”的基础。"		Vue 编译时优化 区块
VUE_TREE_FLATTENING_SSR	基础	前端::7-框架与生态::Vue::渲染机制::编译时优化	“树结构打平”如何影响SSR激活性能？	"“树结构打平”和“更新类型标记”极大地提升了SSR激活（Hydration）的性能。在激活阶段，Vue<b>只需遍历区块节点和它们被打平后的动态子节点列表</b>，而不是整个DOM结构。这使得在模板层面上实现了更高效的“部分激活”，静态部分被完全跳过。"		Vue 编译时优化 SSR激活
