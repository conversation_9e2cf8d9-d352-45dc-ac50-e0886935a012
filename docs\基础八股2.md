
<h1 id="u0MVi">substring和substr的区别</h1>
substring() 和 substr() 都是 JavaScript 字符串的方法，用于截取字符串的一部分。它们的区别在于参数的不同。

substring() 方法接收两个参数，起始位置和结束位置。它截取从起始位置到结束位置之间的字符，包括起始位置的字符，但不包括结束位置的字符。如果省略第二个参数，则截取到字符串末尾。

substr() 方法接收两个参数，起始位置和截取的字符数。它从起始位置开始截取指定数量的字符，如果省略第二个参数，则截取到字符串末尾。

举个例子，假设有一个字符串 str = 'hello world'，使用 substring() 和 substr() 方法分别截取字符串的一部分：

```javascript
str.substring(0, 5); // "hello"
str.substring(6); // "world"
str.substr(0, 5); // "hello"
str.substr(6); // "world"
```



可以看到，当使用 substring() 方法时，第二个参数表示截取的结束位置；而使用 substr() 方法时，第二个参数表示截取的字符数。



<h1 id="uAFGl">js脚本异步加载如何实现 有什么区别</h1>
在 Web 应用中，JavaScript 脚本的异步加载可以通过以下方式来实现：

1. 动态创建 <script> 标签，并设置其 src 属性为需要加载的脚本 URL。这种方式可以通过设置 onload 或 onreadystatechange 事件来检测脚本是否加载完成。

```javascript
const script = document.createElement('script');
script.src = 'path/to/script.js';
script.onload = function() {
 // 脚本加载完成后执行的回调函数
};
document.body.appendChild(script);
```



2. 使用 XMLHttpRequest 对象或 Fetch API 发送异步请求，并在请求成功后将响应文本解析为 JavaScript 代码，然后使用 eval() 函数或 Function() 构造函数来执行脚本。

```javascript
const xhr = new XMLHttpRequest();
xhr.open('GET', 'path/to/script.js');
xhr.onload = function() {
 const script = document.createElement('script');
 script.textContent = xhr.responseText;
 document.head.appendChild(script);
};
xhr.send();
```



使用这两种方式可以实现 JavaScript 脚本的异步加载，相比于同步加载脚本，异步加载具有以下区别：

1. 异步加载可以提高页面的加载速度和响应性能，避免因 JavaScript 阻塞而造成页面卡顿的情况。
2. 异步加载可以避免因加载脚本而造成的阻塞情况，使页面的其他资源可以更快地加载和呈现。
3. 异步加载可以更灵活地控制脚本的加载顺序和执行时间，可以根据页面需要动态加载和卸载脚本，提高页面的可维护性和可扩展性。



<h1 id="p6EOW">for in/for of的区别</h1>
for...in 和 for...of 都是 JavaScript 中的循环语句，但它们的作用和使用方式略有不同。

**for...in** 循环用于遍历对象的可枚举属性，它会将对象的每个属性名称（或键名）作为迭代变量来遍历。以下是 for...in 的基本语法：

```javascript
for (variable in object) {
 // 在此处编写循环体语句
}
```



其中，variable 表示每次迭代中当前属性的名称（或键名），object 表示要遍历的对象。例如：

```javascript
const obj = { a: 1, b: 2, c: 3 };
for (const key in obj) {
 console.log(key, obj[key]);
}
```



输出结果：

```javascript
a 1b 2
c 3
```

需要注意的是，for...in 循环遍历的是对象的可枚举属性，包括自有属性和继承属性。因此，它并不适用于遍历数组和类数组对象。



**for...of **循环用于遍历可迭代对象的元素，它会将对象的每个元素作为迭代变量来遍历。以下是 for...of 的基本语法：

```javascript
for (variable of iterable) {
 // 在此处编写循环体语句
}
```



其中，variable 表示每次迭代中当前元素的值，iterable 表示要遍历的可迭代对象，如数组、字符串、Set、Map 等。例如：

```javascript
const arr = [1, 2, 3];
for (const item of arr) {
 console.log(item);
}
```



输出结果：

```javascript
1
2
3
```



需要注意的是，for...of 循环只能遍历实现了迭代器接口（Iterator）的对象，因此它不适用于普通的对象。此外，它遍历的是对象的元素值，而不是键名或属性名。

总结一下，for...in 适用于遍历对象的属性名，而 for...of 适用于遍历数组、字符串等可迭代对象的元素值。



<h1 id="DmoC9">js中如何判断数据类型</h1>
JavaScript 中的数据类型可以通过多种方式进行判断，下面介绍几种常用的方式：

1. **使用 typeof 操作符**

typeof 操作符可以返回一个值的数据类型，它适用于除了 null 以外的所有值。它的基本语法如下：

<font style="color:rgb(51, 51, 51);">typeof value</font>

其中，value 表示要判断类型的值。返回值包括以下几种：

+ "undefined"：表示值未定义。
+ "boolean"：表示值为布尔值。
+ "number"：表示值为数值。
+ "string"：表示值为字符串。
+ "symbol"：表示值为符号。
+ "object"：表示值为对象或 null。
+ "function"：表示值为函数。

需要注意的是，typeof null 返回 "object"，这是一个历史遗留问题。



2. **使用 instanceof 操作符**

instanceof 操作符可以判断一个对象是否是某个构造函数的实例。它的基本语法如下：

```javascript
object instanceof constructor
```

<font style="color:rgb(51, 51, 51);"></font>

其中，object 表示要判断的对象，constructor 表示构造函数。返回值为布尔值，表示是否为指定构造函数的实例。



3. **使用 Object.prototype.toString 方法**

Object.prototype.toString 方法可以返回一个值的内部类型，它适用于所有值，包括 null 和 undefined。它的基本语法如下：

```javascript
Object.prototype.toString.call(value)
```

<font style="color:rgb(51, 51, 51);"></font>

其中，value 表示要判断类型的值。返回值是一个形如 [object Type] 的字符串，其中 Type 表示值的内部类型。例如：

```javascript
Object.prototype.toString.call(undefined); // "[object Undefined]"
Object.prototype.toString.call(null); // "[object Null]"
Object.prototype.toString.call(true); // "[object Boolean]"
Object.prototype.toString.call(123); // "[object Number]"
Object.prototype.toString.call("abc"); // "[object String]"
Object.prototype.toString.call(Symbol("foo")); // "[object Symbol]"
Object.prototype.toString.call({}); // "[object Object]"
Object.prototype.toString.call(function () {}); // "[object Function]"
Object.prototype.toString.call([]); // "[object Array]"
Object.prototype.toString.call(new Date()); // "[object Date]"
Object.prototype.toString.call(/abc/); // "[object RegExp]"
```

<font style="color:rgb(51, 51, 51);"></font>

需要注意的是，使用 Object.prototype.toString 方法判断基本类型值时，返回的是其包装对象的类型，而不是基本类型本身的类型。

除了上述方法之外，还有一些其他的判断类型的方式，如 Array.isArray 判断数组类型、isNaN 判断是否为 NaN、Number.isInteger 判断是否为整数等。不同的判断方式适用于不同的场景，具体选择哪一种方式应根据实际情况而定。



<h1 id="poFFY">splice和slice会改变原数组吗？怎么删除数组最后一个元素？</h1>
splice() 和 slice() 是 JavaScript 数组的方法，两者作用不同。

1. splice() 方法可以在数组中添加、删除或替换元素，并返回被删除的元素，它会改变原数组。
2. slice() 方法是从原数组中返回指定开始和结束位置的元素组成的新数组，它不会改变原数组。

所以，使用 splice() 方法可能会改变原数组，而 slice() 方法则不会。

要删除数组中的最后一个元素，有几种方法：

1. 使用 pop() 方法删除并返回数组的最后一个元素，即可删除数组的最后一个元素：

```javascript
const arr = [1, 2, 3, 4];
const lastElement = arr.pop(); // 返回被删除的元素 4console.log(arr); // [1, 2, 3]
```



2. 使用 splice() 方法删除数组的最后一个元素：

```javascript
const arr = [1, 2, 3, 4];
arr.splice(-1, 1); // 从倒数第一个位置开始删除一个元素console.log(arr); // [1, 2, 3]
```



3. 使用 slice() 方法和展开运算符 (...) 创建一个新的数组，包含除了最后一个元素以外的所有元素：

```javascript
const arr = [1, 2, 3, 4];
const newArr = [...arr.slice(0, -1)];
console.log(newArr); // [1, 2, 3]
```



需要注意的是，以上方法都会改变或创建一个新的数组，原数组不会被保留最后一个元素。如果要仅仅取得最后一个元素，可以使用下标或者数组方法 slice()：

```javascript
const arr = [1, 2, 3, 4];
const lastElement = arr[arr.length - 1]; // 4const lastElement2 = arr.slice(-1)[0]; // 4
```



<h1 id="mOaza">==和===有什么区别</h1>
在 JavaScript 中，== 和 === 都用于比较两个值是否相等，但它们的比较方式不同。

== 运算符进行比较时，会先进行类型转换，然后再比较两个值是否相等。类型转换的规则比较复杂，但可以简单地概括为以下几点：

1. 如果两个值类型相同，则直接比较它们的值。
2. 如果一个值是 null，另一个值是 undefined，则它们相等。
3. 如果一个值是数字，另一个值是字符串，则将字符串转换为数字后再比较。
4. 如果一个值是布尔值，另一个值是非布尔值，则将布尔值转换为数字后再比较。
5. 如果一个值是对象，另一个值是数字、字符串或布尔值，则将对象转换为原始值后再比较。

例如：

<font style="color:rgb(51, 51, 51);">console.log(1 == "1"); // true，字符串 "1" 被转换为数字 1console.log(true == 1); // true，布尔值 true 被转换为数字 1console.log(null == undefined); // true，它们相等console.log("5" == [5]); // true，字符串 "5" 被转换为数字 5console.log({} == {}); // false，因为对象被转换为原始值后仍然是对象</font>

=== 运算符进行比较时，不进行类型转换，只有当两个值的类型和值都相等时才会返回 true。例如：

<font style="color:rgb(51, 51, 51);">console.log(1 === "1"); // false，它们类型不同console.log(true === 1); // false，它们类型和值不同console.log(null === undefined); // false，它们类型不同console.log("5" === [5]); // false，它们类型不同console.log({} === {}); // false，它们是不同的对象</font>

一般来说，建议优先使用 === 运算符进行比较，因为它可以避免类型转换的问题，更加严格和安全。



<h1 id="kCnO7">解释 requestAnimationFrame/requestIdleCallback，分别有什么用？</h1>
requestAnimationFrame 和 requestIdleCallback 都是用于在浏览器中执行动画或其他高性能任务的 API。

**requestAnimationFrame** 是浏览器提供的一种动画帧请求机制，它会在浏览器下一次绘制之前执行指定的回调函数。这样做的好处是可以在浏览器下一次绘制时，让浏览器自动完成一些复杂的计算和渲染工作，从而避免了浏览器在短时间内重复执行相同的任务。使用 requestAnimationFrame 可以实现更加流畅的动画效果，同时也可以减少页面的闪烁和卡顿。

```javascript
function animate() {
 // 在这里编写动画逻辑requestAnimationFrame(animate);
}
requestAnimationFrame(animate);
```



**requestIdleCallback** 是一个相对较新的 API，它的作用是在浏览器空闲时执行指定的回调函数。这个 API 的目的是让开发者能够在浏览器空闲时，进行一些比较耗时的任务，例如计算和渲染。这样做的好处是可以提高网页的性能和响应速度，同时也可以避免阻塞浏览器的主线程，导致用户体验不佳。

```javascript
function doWork(deadline) {
 while (deadline.timeRemaining() > 0) {
 // 在这里编写任务逻辑
 }
 if (还有任务需要执行) {
 requestIdleCallback(doWork);
 }
}
requestIdleCallback(doWork);
```



需要注意的是，requestIdleCallback 的回调函数接受一个 IdleDeadline 参数，它包含了当前空闲时间的相关信息。开发者可以通过这个参数，根据浏览器的空闲时间进行任务调度和优化。

综上所述，requestAnimationFrame 适用于需要在下一次绘制之前执行的动画任务，而 requestIdleCallback 则适用于需要在浏览器空闲时执行的耗时任务。



<h1 id="sqw7o">let全局声明变量，window能取到吗？</h1>
使用 let 声明的变量不会挂在全局对象 window 上，因此无法通过 window.variableName 的方式访问。这与使用 var 声明的变量不同，var 声明的变量会被挂载在全局对象上，因此可以通过 window.variableName 的方式访问。

举个例子，假设有以下代码：

```javascript
let foo = 'bar';
var baz = 'qux';
console.log(window.foo); // undefinedconsole.log(window.baz); // 'qux'
```



在这段代码中，使用 let 声明的变量 foo 无法通过 window.foo 访问，而使用 var 声明的变量 baz 可以通过 window.baz 访问。



<h1 id="Dg2sW">数组foreach能否结束循环</h1>
数组的 forEach 方法默认不支持提前结束循环，即无法使用类似于 break 或 return 的语法来跳出循环。但是可以使用抛出异常的方式来达到提前结束循环的效果。

例如，可以在 forEach 中抛出一个自定义异常，然后在异常捕获块中终止循环。示例如下：

```javascript
const arr = [1, 2, 3, 4, 5];
let isBreak = false;

try {
  arr.forEach((item) => {
    if (item === 3) {
      throw new Error('break');
    }
    console.log(item);
  });
} catch (e) {
  if (e.message === 'break') {
    console.log('循环提前结束');
  } else {
    throw e;
  }
}
```



在上述示例中，当数组中的元素等于 3 时，抛出一个自定义异常，然后在 catch 块中处理异常，从而实现了提前结束循环的效果。需要注意的是，这种方式并不常用，通常可以使用 for 循环或 some、every 等数组方法来替代。



<h1 id="IOS6n">canvas怎么判断点在图形内？（低频）</h1>
判断一个点是否在 Canvas 中的图形内，通常需要使用数学公式和 Canvas 提供的 API 来实现。

对于简单的图形（如矩形、圆形等），可以使用 Canvas API 中的方法进行判断，例如：

1. 判断点是否在矩形内：使用 [ctx.rect](http://ctx.rect/)() 绘制矩形，再使用 ctx.isPointInPath() 方法判断点是否在矩形内；
2. 判断点是否在圆形内：使用 [ctx.arc](http://ctx.arc/)() 绘制圆形，再使用 ctx.isPointInPath() 方法判断点是否在圆形内。

对于复杂的图形（如多边形、不规则图形等），可以使用数学公式进行判断。例如，对于一个多边形，可以使用射线法来判断点是否在多边形内：

1. 将多边形的每条边与射线相交，计算交点的数量。
2. 如果交点的数量为奇数，则点在多边形内部；如果交点的数量为偶数，则点在多边形外部。

以下是一个简单的使用射线法判断点是否在多边形内的示例代码：

```javascript
function isPointInPolygon(point, polygon) {
  let crossings = 0;
  const x = point.x, y = point.y;
  const n = polygon.length;

  for (let i = 0; i < n; i++) {
    const p1 = polygon[i];
    const p2 = polygon[(i + 1) % n];

    if (((p1.y <= y) && (p2.y > y)) || ((p1.y > y) && (p2.y <= y))) {
      const vt = (y - p1.y) / (p2.y - p1.y);
      const xCross = p1.x + vt * (p2.x - p1.x);

      if (x < xCross) {
        crossings++;
      }
    }
  }

  return (crossings % 2 !== 0);
}
```



对于 Vue 和 React 等框架，判断点是否在图形内的具体实现可能因为应用场景而有所不同。一般来说，可以在组件的 mounted 钩子函数中获取 Canvas 元素，然后在事件处理函数中调用判断点是否在图形内的方法来实现相应的功能。  


<h1 id="gNOaZ">如何合并对象</h1>
可以使用 Object.assign() 或扩展运算符 ... 来合并js对象。下面是两种合并对象的方式：

1. 使用 Object.assign() 合并对象

```javascript
const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const mergedObj = Object.assign({}, obj1, obj2);
console.log(mergedObj); // { a: 1, b: 2, c: 3, d: 4 }
```



2. 使用扩展运算符 ... 合并对象

```javascript
const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const mergedObj = { ...obj1, ...obj2 };
console.log(mergedObj); // { a: 1, b: 2, c: 3, d: 4 }
```



需要注意的是，如果合并的对象中有同名属性，则后面的属性值会覆盖前面的属性值。



<h1 id="EidM4">如何判断一个对象是不是空对象</h1>
可以通过以下两种方式判断一个对象是不是空对象：

1. 使用[Object.keys](http://object.keys/)()方法获取对象的属性列表，然后判断列表长度是否为0。

例如：

```javascript
const obj = {};
if (Object.keys(obj).length === 0) {
 console.log('obj is empty');
}
```



2. 使用[for...in](http://for...in/)循环遍历对象，如果有属性存在则不是空对象。

例如：

```javascript
const obj = {};
let isEmpty = true;
for (const prop in obj) {
 isEmpty = false;
 break;
}
if (isEmpty) {
 console.log('obj is empty');
}
```



两种方式的效果是一样的，可以根据具体情况选择使用。

