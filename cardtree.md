# 前端

## 0-计算机科学基础
*   数据结构与算法
*   设计模式
*   计算机网络
*   操作系统与编译原理

## 1-HTML
*   HTML
*   元素与语义化
*   表单与媒体
*   可访问性-A11y

## 2-CSS
*   选择器与层叠
*   盒模型与视觉格式化
*   布局系统-Flex-Grid
*   过渡与动画
*   响应式设计
*   预处理器-Sass-PostCSS

## 3-JavaScript
*   1-语法与数据结构
    *   instanceof
    *   new
    *   浮点数精度
    *   Symbol
    *   typeof
*   2-执行上下文-作用域-闭包
    *   作用域
    *   闭包
    *   bind
*   3-原型与继承
    *   原型链
*   4-异步编程-EventLoop-Promise
    *   EventLoop
    *   Promise
*   5-ESNext与模块化
    *   ES6
*   6-函数式编程

## 4-TypeScript
*   类型系统
*   工程应用

## 5-浏览器与WebAPI
*   渲染机制-回流-重绘
*   DOM操作
*   BOM与事件
    *   事件流
*   存储-Cookie-Storage
*   Web安全-XSS-CSRF-CORS
*   Web Worker
*   浏览器
*   DNS
*   TCP
*   Web Component
*   Canvas
*   AJAX
*   CDN

## 6-工程化与工具链
*   构建工具-Vite-Webpack
*   包管理器-npm-pnpm
*   代码质量-ESLint-Prettier
*   测试-Jest-Cypress
*   CI-CD与Docker

## 7-框架与生态
### Vue
*   核心思想
*   渲染机制
*   响应式原理
*   组件化
*   生态-Router-Pinia
*   架构与设计
*   性能与用户体验
### React
*   Hook原理
*   组件化与状态
*   生态-Router-Redux

## 8-架构与设计
*   组件化设计
*   状态管理模式
*   微前端架构
*   代码规范与整洁

## 9-性能与用户体验
*   性能指标与监控
*   优化策略
*   国际化-i18n
*   交互与动效

## 10-Node与跨端
*   NodeJS
*   全栈框架-NestJS
*   跨端方案-小程序-Electron

## 11-面试与职业发展
*   算法题
*   系统设计题
*   软技能