## 谈谈你对vue的理解
#### 点题收敛
Vue是一个渐进式JavaScript框架，它专注于构建用户界面。Vue的核心思想是数据驱动和组件化。通过将页面拆分成独立的组件，可以更好地管理代码，提高代码的复用性和可维护性。



#### 追问拓展
Vue的优势在于其简单易用、灵活性高、性能卓越和扩展性强。Vue的模板语法易于理解和学习，可以快速构建交互式的Web应用程序。同时，Vue的生命周期钩子和自定义指令等功能，使得Vue可以满足各种复杂的需求。另外，Vue还提供了Vuex、Vue Router等官方插件，可以进一步扩展Vue的功能。

Vue的响应式数据绑定机制是Vue最核心的特性之一。通过对数据进行劫持和监听，可以实现数据的双向绑定，即数据变化会自动更新视图，同时视图的变化也会反映到数据上。这种机制使得Vue的数据流非常清晰和可预测，同时也减少了开发的工作量。

总之，我认为Vue是一个优秀的JavaScript框架，它简单易用、功能强大、扩展性好，并且有着极佳的性能表现。对于前端开发人员来说，Vue是一个值得深入学习和使用的框架。



## vue next tick实现原理
#### 点题收敛
在 [Vue.js](http://vue.js/) 中，当我们对数据进行修改时，[Vue.js](http://vue.js/) 会异步执行 DOM 更新。在某些情况下，我们需要在 DOM 更新完成后执行一些操作，这时就需要使用 Vue.nextTick() 方法。



#### 详细拓展
Vue.nextTick() 方法的实现原理是基于浏览器的异步任务队列，采用微任务优先的方式。当我们修改数据时，[Vue.js](http://vue.js/) 会将 DOM 更新操作放到一个异步任务队列中，等待下一次事件循环时执行。而 Vue.nextTick() 方法则是将一个回调函数推入到异步任务队列中，等待 DOM 更新完成后执行。

具体实现方式有以下几种：

**使用原生的 setTimeout 方法**：在 [Vue.js](http://vue.js/) 2.x 中，如果浏览器支持 Promise，则会优先使用 [Promise.then](http://promise.then/)() 方法。如果不支持 Promise，则会使用原生的 setTimeout 方法模拟异步操作。

**使用 MutationObserver**：如果浏览器支持 MutationObserver，[Vue.js](http://vue.js/) 会使用 MutationObserver 监听 DOM 更新，并在 DOM 更新完成后执行回调函数。

**使用 setImmediate**：在 IE 中，setImmediate 方法可以用来延迟异步执行任务。在 [Vue.js](http://vue.js/) 2.x 中，如果浏览器支持 setImmediate，则会优先使用 setImmediate，否则会使用 setTimeout。



#### 最后收敛
总之，Vue.nextTick() 的实现原理是利用浏览器的异步任务队列，在 DOM 更新完成后执行回调函数。不同浏览器支持的异步任务方法不同，[Vue.js](http://vue.js/) 会根据浏览器的支持情况选择合适的异步任务方法。



#### Vue.nextTick()的意义在哪里（理解一下）
**关键点：确保我们操作的是更新后的 DOM；这样做可以避免频繁的 DOM 操作，提高性能。**

Vue.nextTick() 的意义在于它可以让我们在下次 DOM 更新循环结束后执行回调函数，确保我们操作的是更新后的 DOM。

[Vue.js](http://vue.js/) 采用异步更新机制来提高渲染效率，当我们修改数据时，[Vue.js](http://vue.js/) 不会立即更新 DOM，而是将 DOM 更新操作放到一个异步队列中，等到下一次事件循环时再执行。这样做可以避免频繁的 DOM 操作，提高性能。

但是，由于 [Vue.js](http://vue.js/) 的异步更新机制，当我们修改数据后，如果想要立即获取更新后的 DOM，可能会出现获取到的是更新前的 DOM 的情况。这时就需要使用 Vue.nextTick() 方法。

Vue.nextTick() 方法可以将回调函数推入到异步队列中，在 DOM 更新完成后执行。这样就可以确保我们操作的是更新后的 DOM，而不是更新前的 DOM。比如在某些情况下需要获取某个元素的尺寸、位置等属性时，如果不使用 Vue.nextTick()，可能会获取到错误的结果。

因此，Vue.nextTick() 是一个非常实用的方法，能够确保我们在操作 DOM 时获取到更新后的结果，提高代码的可靠性。

## vue 在渲染列表的时候，为什么不建议用数组的下标当做列表的key值
#### 点题收敛
因为要保证渲染列表的性能和正确性



#### 详细回答
在Vue渲染列表时，每个元素需要一个唯一的key值来标识自己，这个key值会被用来判断列表中哪些元素需要更新、删除或新增。如果使用数组的下标作为key值，虽然可以满足每个元素key值唯一的需求，但是由于Vue的更新机制是基于diff算法实现的，使用数组下标作为key值会导致Vue无法正确地判断列表中元素的变化情况。

具体来说，如果将数组下标作为key值，那么当列表发生变化时，可能会导致key值发生改变，从而引发不必要的组件重新渲染，甚至会导致性能问题。例如，当删除列表中某个元素时，其后面的所有元素的下标都会发生改变，导致Vue重新渲染整个列表。

为了避免这个问题，我们需要为每个元素提供一个稳定的、与其内容相关的唯一key值，例如使用元素的id属性作为key值。这样，当列表中某个元素的内容发生变化时，其对应的key值也会发生改变，从而告诉Vue需要更新该元素。



#### 总结收敛
总之，为了保证Vue渲染列表的性能和正确性，我们应该尽量避免使用数组下标作为key值。



## 谈一下对vuex的理解
#### 点题收敛
Vuex是一个专门为[Vue.js](http://vue.js/)开发的状态管理库，它提供了一个集中式的状态管理机制，用于管理Vue应用中的所有组件的共享状态。Vuex的核心思想是将组件的共享状态抽离出来，以单独的状态树的形式存储，然后通过定义一系列的mutations、actions、getters来操作这个状态树。



#### 详细回答
Vuex的核心概念包括：state、mutations、actions和getters。其中，state是应用的状态，而mutations用于修改state中的状态。actions则用于处理异步操作或批量的同步操作，最终通过mutations来改变state。getters则用于对state中的数据进行计算或过滤。

在Vuex中，数据流的流向是单向的，即从state到组件，再从组件到mutations/actions。这种单向数据流的机制使得数据的流动更加清晰，同时也更容易进行调试和维护。而Vuex还提供了一些辅助函数，比如mapState、mapGetters、mapActions和mapMutations等，用于方便地访问和操作状态树。



#### 总结收敛
总之，Vuex是[Vue.js](http://vue.js/)生态中的一个非常重要的插件，适用于中大型的[Vue.js](http://vue.js/)应用，它通过提供集中式的状态管理机制，帮助我们更好地管理数据流，提高应用的可维护性和可扩展性。同时，Vuex还有一些高级特性，比如模块化的状态管理和插件机制，能够进一步提高我们的开发效率。



## vue-router有哪几种导航钩子
Vue Router提供了多种导航钩子，可以在导航过程中执行相应的操作。下面是Vue Router中常用的导航钩子：

**beforeEach**: 在每次路由跳转之前执行，可以用来进行用户身份验证、路由拦截等操作。

**beforeResolve**: 在导航被确认之前，同时在所有组件内守卫和异步路由组件被解析之后执行。

**afterEach**: 在每次路由跳转之后执行，可以用来进行路由跳转后的操作，比如页面滚动、统计PV等操作。

**beforeEnter**: 在进入路由之前执行，与全局beforeEach的区别是它可以针对某个具体路由进行设置。

**beforeRouteUpdate**: 在路由更新时执行，比如路由参数发生变化时。

**beforeRouteLeave**: 在离开当前路由时执行，可以用来进行页面数据的保存或弹出提示等操作。



这些导航钩子提供了灵活的路由跳转控制机制，可以方便地实现各种复杂的路由跳转需求。同时，Vue Router还提供了一些其他的导航钩子和高级特性，比如路由元信息、动态路由、命名路由等，可以进一步提高开发效率和应用的可维护性。



## vue-router的核心原理
#### 收敛点题
Vue Router是[Vue.js](http://vue.js/)官方提供的一款路由管理器，它通过监听URL变化，匹配路由规则，展示对应的组件内容，从而实现单页应用的路由控制。



#### 详细深入
Vue Router的核心原理包括以下几个方面（**这玩意记不住就先说下面的总结收敛的，然后再回忆，相对低频**）：

**路由匹配**：Vue Router通过定义路由规则来匹配URL路径，并根据匹配结果展示对应的组件内容。路由规则可以使用路径、参数、查询参数等多种方式进行定义，同时支持嵌套路由和命名路由等高级特性。

**路由模式**：Vue Router支持两种路由模式，分别是Hash模式和History模式。在Hash模式下，路由信息会被保存在URL的Hash部分，通过监听Hash变化来进行路由控制；在History模式下，路由信息会被保存在浏览器的History API中，通过修改浏览器历史记录来进行路由控制。

**路由导航**：Vue Router中的导航钩子可以监听路由变化，进行路由拦截、身份验证等操作。导航钩子包括全局导航钩子和组件内导航钩子，可以在路由跳转前、跳转后、路由更新等不同阶段执行相应的逻辑。

**路由组件**：Vue Router通过组件的动态加载来实现异步路由组件，可以根据需要动态加载路由组件，从而提高应用的性能和用户体验。同时，Vue Router还支持路由懒加载、路由元信息等高级特性，可以进一步提高应用的灵活性和可维护性。



#### 总结收敛
总之，Vue Router是实现[Vue.js](http://vue.js/)单页应用路由控制的核心组件之一，它通过路由匹配、路由模式、路由导航、路由组件等多个方面实现了完整的路由控制逻辑，为开发者提供了强大的路由控制能力。



## Vue Router history 模式上线需要注意什么事项
Vue Router的History模式相比于默认的Hash模式来说，能够更好地模拟传统的多页面应用的URL地址，让用户体验更加自然。但是，使用History模式需要注意以下几点事项：

后端配置：使用History模式需要后端对所有可能的路由路径都进行处理，以避免在刷新或直接输入URL时出现404错误。后端配置的方式取决于后端服务器的类型，如Apache、Nginx等，需要在服务器上进行相关配置。

安全性：使用History模式会暴露出服务器上的文件路径，因此需要特别注意安全性。在部署时需要仔细检查服务器配置，确保不会因为恶意请求而导致安全问题。

兼容性：History模式需要支持HTML5的history.pushState API，因此在一些较老的浏览器上可能会存在兼容性问题。需要在开发时做好相关的测试和兼容性处理。

打包发布：在使用Webpack等工具打包发布时，需要配置正确的publicPath，保证HTML中引用的资源路径正确。同时需要注意，如果项目使用了多个子路由，需要在打包时将所有的子路由都配置到publicPath中。

总之，使用History模式需要对后端进行相关配置，并且需要特别注意安全性和兼容性问题，同时在打包发布时需要正确配置publicPath，确保页面资源路径正确。



## Vue Router history 模式为什么刷新出现404
#### 点题收敛
原因是因为浏览器在刷新页面时会向服务器发送GET请求，但此时服务器并没有相应的资源来匹配这个请求，因为在History模式下，所有路由都是在前端路由中实现的，并没有对应的后端资源文件。



#### 解决方案
为了解决这个问题，我们需要在服务器端进行相关配置，让所有的路由都指向同一个入口文件（比如[index.html](http://index.html/)），由前端路由来处理URL请求，返回对应的页面内容。具体的配置方式取决于服务器类型，常见的有Apache、Nginx等。

以Nginx为例，可以在Nginx的配置文件中加入如下代码：  


```vue
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
}
```



这段代码会将所有请求都指向根目录下的[index.html](http://index.html/)文件，让前端路由来处理URL请求。同时需要注意，在使用History模式时需要保证所有路由的访问路径都指向[index.html](http://index.html/)，否则仍然会出现404错误。

## 用vue-router hash模式实现锚点
使用Vue Router的Hash模式可以实现锚点跳转。

首先，在Vue Router的路由配置中，需要将mode设置为hash：

```javascript
const router = new VueRouter({
  mode: 'hash',
  routes: [
    // 路由配置
  ]
})
```



然后，在需要跳转的地方，使用[router.push](http://router.push/)方法进行路由跳转，设置目标URL的hash部分为锚点的名称：

```javascript
this.$router.push({ path: '/yourpath#youranchor' })
```

其中，yourpath为目标路由路径，youranchor为目标锚点名称。



接着，在目标组件中，可以使用Vue的生命周期函数mounted来获取目标锚点的DOM元素，并使用scrollIntoView方法将其滚动到视图中：

```javascript
mounted () {
  const anchor = document.getElementById('youranchor')
  if (anchor) {
    anchor.scrollIntoView()
  }
}
```

其中，youranchor为目标锚点的名称，可以在模板中使用id属性设置。



这样，当路由跳转到目标页面时，页面会自动滚动到指定的锚点位置。



## 说下虚拟DOM和diff算法，key的作用
虚拟DOM和diff算法是React\vue中的两个核心概念。

虚拟DOM是指用JavaScript对象模拟DOM树结构，包括节点的类型、属性和子节点等信息。当状态发生变化时，React会使用新的状态生成一个新的虚拟DOM树，并通过对比新旧虚拟DOM树的差异（diff算法），计算出需要更新的节点，最终只更新需要更新的节点，从而提高性能。

diff算法是指在两个树形结构之间找出差异的算法。在React中，通过对比新旧虚拟DOM树节点的不同，分为以下三种情况：

1. 替换节点：节点的类型发生了变化，例如从div变成了p。
2. 更新属性：节点的属性发生了变化，例如class、style等。
3. 更新子节点：节点的子节点发生了变化。



在diff算法的过程中，key的作用是给每个虚拟DOM节点添加一个唯一的标识符。这样在进行新旧虚拟DOM对比时，可以通过key值的对比快速判断是否是同一个节点，避免不必要的DOM操作。如果不添加key，diff算法只能通过遍历子节点的方式查找，效率较低。



Vue 也采用了虚拟DOM和diff算法来提高渲染性能。

在 Vue 中，虚拟DOM的概念被称为“VNode”，它是一个轻量级的JavaScript对象，用于描述DOM节点。当数据发生变化时，Vue 会创建一个新的VNode树，并通过diff算法来对比新旧VNode树，找到最小变更并进行渲染。这样可以避免对整个DOM树进行重绘，提高性能。

Vue中的key属性用于标识节点的唯一性，当节点需要移动时，key可以帮助Vue更准确地定位节点，避免不必要的操作。如果没有使用key，Vue会尝试通过就地复用和移动算法来尽可能减少DOM操作，但这可能会导致一些意外的行为，例如，数据不一致、输入框内容丢失等。

总之，Vue的虚拟DOM和diff算法与React类似，但具体实现和一些细节可能有所不同。



## vue2和vue3有哪些区别？
[Vue.js](http://vue.js/)是一款流行的前端框架，其版本迭代也较为频繁。[Vue.js](http://vue.js/) 3 是 [Vue.js](http://vue.js/) 的最新版本，相较于 [Vue.js](http://vue.js/) 2，有以下主要的区别：

1. 性能提升：[Vue.js](http://vue.js/) 3 在内部实现上进行了大量的优化，使得渲染速度更快，内存占用更少。
2. Composition API：[Vue.js](http://vue.js/) 3 引入了 Composition API，可以更好地组织和复用逻辑代码，提高代码的可维护性。
3. 更好的TypeScript支持：[Vue.js](http://vue.js/) 3 对 TypeScript 的支持更加友好，提供了完整的类型定义。
4. 更好的Tree Shaking支持：[Vue.js](http://vue.js/) 3 支持更好的 Tree Shaking，可以更加精确地按需引入需要的模块。
5. 更少的依赖：[Vue.js](http://vue.js/) 3 的核心库的依赖更少，可以减小打包体积。
6. 更多的特性：[Vue.js](http://vue.js/) 3 支持更多的特性，如片段和Teleport等。

总的来说，[Vue.js](http://vue.js/) 3 在性能、可维护性和特性上都有所提升。但是需要注意的是，由于 API 发生了较大的变化，因此 [Vue.js](http://vue.js/) 3 与 [Vue.js](http://vue.js/) 2 之间并不完全兼容，需要进行相应的迁移工作。



## vue项目中style样式中为什么要添加 scoped
在Vue中使用 scoped 属性可以让样式作用域仅限于当前组件中，不影响全局，避免了样式污染和样式冲突的问题。

scoped 会为每个组件的 style 标签添加一个唯一的属性作为标记，这样每个组件的样式规则只作用于当前组件的元素，不影响其他组件的样式。

在组件中使用 scoped 的方式如下：

```html
<template>
  <div class="example">
    <p>Example Component</p>
  </div>
</template>

<style scoped>
.example {
  color: red;
}
</style>
```



在这个例子中，.example 样式规则只会作用于当前组件内部的元素，而不会影响全局的样式。



## Vue2修改了数组哪些方法，为什么
Vue2修改了以下数组方法：

1. **push()**
2. **pop()**
3. **shift()**
4. **unshift()**
5. **splice()**
6. **sort()**
7. **reverse()**

这些方法都是可以改变原数组的。为了实现数据响应式更新，Vue2在这些方法中添加了特定的代码，以便通知 Vue 视图更新数据。

举个例子，当我们通过 push() 方法向一个数组中添加元素时，Vue2 会检测到这个数组发生了变化，并通知 Vue 视图更新相关数据。这个操作不需要我们手动去更新视图，Vue2 会帮我们完成。

需要注意的是，如果我们使用非响应式的方式来更新数组，例如直接修改数组中某个元素的值，Vue2 就无法监听到这个变化。所以我们需要遵循 Vue2 的修改数组的规范，才能让 Vue2 正常响应式更新数据。



## mounted生命周期和keep-alive中activated的优先级
在 Vue 中，mounted 生命周期是指一个组件被挂载到 DOM 中后触发的钩子函数。而 keep-alive 是一个用来缓存组件的抽象组件，它自身没有任何展示效果，只是将内部包含的组件缓存起来，从而能够在需要时快速地切换到缓存的组件。

当一个组件第一次被挂载时，mounted 生命周期会被触发，同时 keep-alive 中的缓存组件还没有被渲染，因此 activated 生命周期并不会被触发。只有当一个被缓存的组件被激活后（比如从其他页面返回到该组件所在的页面），activated 生命周期才会被触发。因此，优先级上 mounted 生命周期高于 activated 生命周期。



## Vue 3.0 使用的 diff 算法相比 Vue 2.0 中的双端比对有以下优势
1. 最长递增子序列算法：Vue 3.0 的 diff 算法采用了最长递增子序列算法，能够减少不必要的 DOM 操作，提升性能。
2. 静态标记：Vue 3.0 中，编译器会对静态节点进行标记，在更新时可以直接跳过这些静态节点，减少 DOM 操作，提升性能。
3. 缓存数组：Vue 3.0 中，每次更新时会将新旧 VNode 数组缓存起来，只对数组中不同的 VNode 进行比对，减少比对次数，提升性能。
4. 动态删除操作：Vue 3.0 中，对于动态删除操作，采用了异步队列的方式进行，能够将多个删除操作合并为一个，减少 DOM 操作，提升性能。

总的来说，Vue 3.0 的 diff 算法相比 Vue 2.0 更加高效，能够减少不必要的 DOM 操作，提升应用的性能。



## vue父子组件钩子的执行顺序是什么
在 Vue 中，父子组件之间的生命周期钩子执行顺序如下：

1. 加载阶段
2. 父组件 beforeCreate 钩子
3. 父组件 created 钩子
4. 父组件 beforeMount 钩子
5. 子组件 beforeCreate 钩子
6. 子组件 created 钩子
7. 子组件 beforeMount 钩子
8. 子组件 mounted 钩子
9. 父组件 mounted 钩子
10. 更新阶段
11. 父组件 beforeUpdate 钩子
12. 子组件 beforeUpdate 钩子
13. 子组件 updated 钩子
14. 父组件 updated 钩子
15. 销毁阶段
16. 父组件 beforeDestroy 钩子
17. 子组件 beforeDestroy 钩子
18. 子组件 destroyed 钩子
19. 父组件 destroyed 钩子

在这个过程中，子组件的生命周期钩子的执行顺序总是在父组件的生命周期钩子之后。在 keep-alive 组件中，由于缓存组件会被 keep-alive 管理，因此在组件被激活或停用时，执行的生命周期钩子会发生变化：

1. 激活（activated）缓存中的组件时：
2. 父组件 activated 钩子
3. 子组件 activated 钩子
4. 停用（deactivated）缓存中的组件时：
5. 父组件 deactivated 钩子
6. 子组件 deactivated 钩子

```javascript
<template>
  <keep-alive>
    <test-a />
  </keep-alive>
</template>
```

在这种情况下，<test-a> 是被 keep-alive 管理的缓存组件，并且它的直接父级组件就是 <keep-alive> 组件。因此，在激活（activated）或停用（deactivated） <test-a> 时，会触发 <keep-alive> 组件的相应钩子函数（即父组件的 activated 或 deactivated 钩子函数）。



需要注意的是，在 keep-alive 组件中，子组件的 mounted 钩子只会在组件被首次渲染时执行，当组件被缓存并再次激活时，子组件的 mounted 钩子不会再次执行，而是执行 activated 钩子。同样，当组件被停用时，子组件的 destroyed 钩子不会立即执行，而是等到组件被销毁时才会执行。



## vue Data里面如果有数组，如何检测数组的变化
Vue可以使用 watch 和 computed 监听数组的变化。

使用 watch 监听数组：可以通过 deep 选项深度监听数组内部元素的变化。

```vue
data() {
  return {
    arr: [1, 2, 3]
  }
},
  watch: {
    arr: {
      handler(newVal, oldVal) {
        console.log('数组变化：', newVal, oldVal)
      },
      deep: true
    }
  }
```



使用 computed 监听数组：创建一个计算属性，返回数组的长度或者某个数组元素的值，当数组发生变化时，计算属性会自动更新。

```vue
data() {
  return {
    arr: [1, 2, 3]
  }
},
computed: {
  arrLength() {
    return this.arr.length
  }
}
```

这种方式只能监听数组长度的变化，不能监听数组内部元素的变化。如果需要监听数组内部元素的变化，可以使用 watch，或者将数组改为响应式数组。



将数组改为响应式数组：通过 [Vue.set](http://vue.set/) 或者 this.$set 方法向数组中添加元素，通过 [Vue.delete](http://vue.delete/) 或者 this.$delete 方法删除数组中的元素，Vue会自动监听数组的变化。

```vue
data() {
  return {
    arr: [1, 2, 3]
  }
},
methods: {
  addItem() {
    this.$set(this.arr, this.arr.length, 4)
  },
  removeItem() {
    this.$delete(this.arr, this.arr.length - 1)
  }
}
```



当使用 [Vue.set](http://vue.set/) 或者 this.$set 方法向数组中添加元素时，Vue会自动将新添加的元素转换成响应式数据，这个新的响应式数据会添加到响应式数组中，并且通知组件重新渲染。当使用 [Vue.delete](http://vue.delete/) 或者 this.$delete 方法删除数组中的元素时，Vue会自动删除数组中的元素，并通知组件重新渲染。



## vue和react的区别
Vue和React都是当前非常流行的前端框架，它们都具有一定的相似之处，但也有很多不同点。以下是Vue和React的一些主要区别：

1. **模板语法不同**：Vue使用基于HTML的模板语法，可以将模板直接渲染成DOM元素。React则采用JSX语法，通过JSX语法直接描述UI组件的结构和样式，再通过React的渲染函数将其转化为真实的DOM元素。
2. **状态管理不同：**Vue提供了VueX状态管理库，使得状态管理变得简单和易于维护。React则提供了React Context和Redux等状态管理工具，让状态管理更加灵活和可控。
3. **组件通信不同：**Vue使用prop和事件的方式进行父子组件之间的通信，同时也支持Vuex进行组件间通信。React则主要通过props和回调函数的方式进行父子组件之间的通信，同时也支持Redux进行跨组件通信。
4. **生命周期不同：**Vue的生命周期包含了8个钩子函数，比较细致且易于理解和掌握。React的生命周期包含了10个钩子函数，其中有些钩子函数是过时的，也有一些新的钩子函数被引入。
5. **响应式原理不同：**Vue使用双向数据绑定的响应式原理来实现数据的自动更新，通过观察数据变化来自动更新页面。React则通过单向数据流来实现组件的数据更新，数据只能从父组件传递到子组件。
6. **性能优化不同：**Vue采用模板编译和虚拟DOM技术来实现高效的渲染，同时还提供了异步组件和keep-alive等性能优化工具。React则采用虚拟DOM和Diff算法来实现高效的渲染，同时还提供了React.PureComponent和React.memo等优化工具。



## vue 父子组件传值有哪些方式
Vue父子组件之间传递数据的方式有以下几种：

**Props：**通过向子组件传递属性的方式实现数据传递。在父组件中通过v-bind绑定子组件的属性，子组件中通过props接收父组件传递的数据。这是一种单向数据流的方式，父组件可以向子组件传递数据，但是子组件不能直接修改传递过来的数据，需要通过触发事件的方式通知父组件进行修改。

**事件：**父组件通过$emit方法触发子组件的自定义事件，子组件中通过$on监听事件并接收参数，从而实现数据的传递。这也是一种单向数据流的方式，父组件通过事件向子组件传递数据，子组件可以通过触发事件的方式通知父组件进行修改。

**$parent/$children：**通过访问父组件或子组件的实例属性来实现数据的传递。但是这种方式不够直观，且容易出现问题，因为父组件或子组件的实例属性可能会在不同的组件结构中发生变化。

**$refs：**通过在父组件中使用ref属性来获取子组件的实例，从而可以直接访问子组件的属性和方法。这种方式也不够直观，且容易出现问题，因为在组件结构复杂的情况下，$refs可能会变得混乱。



其中，Props是最常用的一种方式，因为它不仅可以实现数据的传递，还可以进行数据类型检查和默认值设置，使得数据的传递更加稳定和安全。

